# ETuto - E-Learning Platform

A comprehensive e-learning platform built with modern technologies.

## Tech Stack

### Backend
- **Language**: Go (Golang)
- **Framework**: Gin
- **ORM**: GORM
- **Database**: PostgreSQL

### Frontend
- **Framework**: React with Vite
- **Language**: TypeScript
- **UI Framework**: Bootstrap
- **State Management**: React Hooks (no Context)

## Features

### Core Functionality
- 🔐 User Authentication & Authorization (<PERSON><PERSON>, Teacher, Student)
- 📚 Course Management with Lessons and Slides
- 📝 Quiz System with Multiple Question Types
- 📊 Progress Tracking and Reporting
- 🖼️ Image Support for Slides and Questions
- 📤 Bulk Question Import (CSV/Excel)

### User Roles
- **Admin**: Full system management
- **Teacher**: Course and content creation
- **Student**: Learning and quiz taking

## Project Structure

```
etuto/
├── backend/          # Go backend API
├── frontend/         # React frontend
├── database/         # Database migrations and seeds
├── docs/            # Documentation
└── docker-compose.yml # Development environment
```

## Getting Started

### Prerequisites
- Go 1.21+
- Node.js 18+
- PostgreSQL 14+
- Docker (optional)

### Development Setup

1. **Backend Setup**
   ```bash
   cd backend
   go mod init etuto-backend
   go mod tidy
   go run main.go
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb etuto_dev
   
   # Run migrations (auto-migration via GORM)
   ```

## API Documentation

The API will be available at `http://localhost:8080` with the following main endpoints:

- `/api/auth/*` - Authentication
- `/api/users/*` - User management
- `/api/courses/*` - Course management
- `/api/lessons/*` - Lesson management
- `/api/quizzes/*` - Quiz management
- `/api/questions/*` - Question bank
- `/api/progress/*` - Progress tracking

## License

MIT License
