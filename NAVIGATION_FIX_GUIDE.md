# Sửa lỗi Navigation trong Slide Editor

## Vấn đề đã được giải quyết

**Vấn đề**: <PERSON><PERSON> từ màn edit course chuyển sang tạo/chỉnh sửa slide, nhấn "Back" lại quay về màn show course thay vì quay về màn edit course.

**Giải pháp**: Thêm tham số URL để tracking nguồn gốc navigation và điều hướng chính xác.

## Thay đổi đã thực hiện

### 1. CourseEdit.tsx
```typescript
// Trước
navigate(`/lessons/${selectedLesson}/slides/create`);
navigate(`/slides/${slideId}/edit`);

// Sau
navigate(`/lessons/${selectedLesson}/slides/create?from=course-edit&courseId=${courseId}`);
navigate(`/slides/${slideId}/edit?from=course-edit&courseId=${courseId}&lessonId=${selectedLesson}`);
```

### 2. SlideCreate.tsx
```typescript
// Thêm logic đọc tham số
const [searchParams] = useSearchParams();
const fromCourseEdit = searchParams.get('from') === 'course-edit';

// Cập nhật navigation logic
const handleSave = async (title: string, content: string) => {
  // ... save logic
  
  // Navigate back based on where we came from
  if (fromCourseEdit && courseId) {
    navigate(`/courses/${courseId}/edit`);
  } else if (lesson) {
    navigate(`/lessons/${lessonId}`);
  } else if (courseId) {
    navigate(`/courses/${courseId}`);
  } else {
    navigate('/courses');
  }
};

const handleCancel = () => {
  if (fromCourseEdit && courseId) {
    navigate(`/courses/${courseId}/edit`);
  } else if (lesson) {
    navigate(`/lessons/${lessonId}`);
  } else if (courseId) {
    navigate(`/courses/${courseId}`);
  } else {
    navigate('/courses');
  }
};
```

### 3. SlideEdit.tsx
```typescript
// Tương tự SlideCreate, thêm logic đọc tham số và cập nhật navigation
const [searchParams] = useSearchParams();
const fromCourseEdit = searchParams.get('from') === 'course-edit';
const courseId = searchParams.get('courseId');
const lessonId = searchParams.get('lessonId');
```

## Luồng Navigation mới

### Từ Course Edit → Slide Create
```
CourseEdit
  ↓ (click "Create with Editor")
SlideCreate?from=course-edit&courseId=123
  ↓ (save/cancel)
CourseEdit (quay lại đúng màn hình)
```

### Từ Course Edit → Slide Edit
```
CourseEdit
  ↓ (click Edit slide)
SlideEdit?from=course-edit&courseId=123&lessonId=456
  ↓ (save/cancel)
CourseEdit (quay lại đúng màn hình)
```

### Từ Lesson Viewer → Slide Create/Edit (không thay đổi)
```
LessonViewer
  ↓
SlideCreate/SlideEdit (không có tham số from)
  ↓
LessonViewer (như cũ)
```

## Cách test

### Test Case 1: Navigation từ Course Edit
1. Vào `/courses`
2. Click "Edit" trên một course
3. Tab "Lessons & Slides"
4. Chọn một lesson
5. Click "Create with Editor" → Kiểm tra URL có `?from=course-edit&courseId=X`
6. Click "Back" hoặc "Cancel" → Phải quay về Course Edit
7. Tạo slide và Save → Phải quay về Course Edit

### Test Case 2: Navigation từ Course Edit - Edit Slide
1. Từ Course Edit (có slides)
2. Click Edit (icon Edit3) trên một slide
3. Kiểm tra URL có `?from=course-edit&courseId=X&lessonId=Y`
4. Click "Back" hoặc "Cancel" → Phải quay về Course Edit
5. Edit và Save → Phải quay về Course Edit

### Test Case 3: Navigation từ Lesson Viewer (không thay đổi)
1. Vào `/lessons/123`
2. Click "Create Slide" hoặc "Edit Slide"
3. URL không có tham số `from`
4. Back/Cancel/Save → Quay về Lesson Viewer

### Test Case 4: Navigation từ Slide Management
1. Vào `/lessons/123/slides`
2. Click "Create" hoặc "Edit"
3. Back/Cancel/Save → Quay về Slide Management

## URL Parameters

### SlideCreate
```
/lessons/:lessonId/slides/create
?from=course-edit          // Đến từ course edit
&courseId=123             // ID của course để quay lại
```

### SlideEdit
```
/slides/:slideId/edit
?from=course-edit          // Đến từ course edit
&courseId=123             // ID của course để quay lại
&lessonId=456             // ID của lesson (optional)
```

## Backward Compatibility

- **Không breaking change**: Các URL cũ vẫn hoạt động bình thường
- **Fallback logic**: Nếu không có tham số `from`, sử dụng logic cũ
- **Optional parameters**: Tất cả tham số đều optional

## Code Quality

### Type Safety
```typescript
const fromCourseEdit = searchParams.get('from') === 'course-edit';
const courseId = searchParams.get('courseId');
const lessonId = searchParams.get('lessonId');
```

### Error Handling
- Kiểm tra tồn tại của courseId trước khi navigate
- Fallback về logic cũ nếu thiếu tham số
- Không crash nếu URL malformed

### Maintainability
- Logic rõ ràng và dễ hiểu
- Tách biệt concerns
- Dễ extend cho các nguồn khác

## Future Enhancements

### Có thể mở rộng cho:
```typescript
// Từ Dashboard
?from=dashboard

// Từ Search Results
?from=search&query=...

// Từ Notifications
?from=notification&notificationId=...
```

### Advanced Navigation
```typescript
// Stack-based navigation
const navigationStack = ['dashboard', 'courses', 'course-edit'];

// Breadcrumb-based navigation
const breadcrumb = [
  { label: 'Courses', path: '/courses' },
  { label: 'Course Name', path: '/courses/123' },
  { label: 'Edit', path: '/courses/123/edit' }
];
```

## Kết luận

Đã sửa thành công lỗi navigation bằng cách:

1. **Thêm URL parameters** để tracking nguồn gốc
2. **Cập nhật logic navigation** trong SlideCreate và SlideEdit
3. **Maintain backward compatibility** với các luồng hiện tại
4. **Improve UX** bằng navigation chính xác

Người dùng giờ đây sẽ được đưa về đúng màn hình mà họ đến từ đó, tạo ra trải nghiệm mượt mà và trực quan hơn.
