# User Management System

## Tổng quan

Hệ thống quản lý người dùng cho phép Admin thực hiện các thao tác CRUD (Create, Read, Update, Delete) trên tài khoản người dùng và quản lý vai trò (roles).

## Tính năng

### 🔐 Phân quyền
- **Admin**: <PERSON><PERSON> quyền truy cập đầy đủ vào trang User Management
- **Teacher/Student**: Không thể truy cập trang User Management

### 👥 Quản lý User
- **Xem danh sách**: Hiển thị tất cả users với thông tin chi tiết
- **Tạo user mới**: Thêm user với email, password, tên và role
- **Chỉnh sửa user**: C<PERSON><PERSON> nhật thông tin user (trừ password)
- **Xóa user**: <PERSON>óa user (không thể xóa chính mình)
- **Qu<PERSON>n lý trạng thái**: <PERSON><PERSON><PERSON> ho<PERSON>/vô hiệu hóa tài khoản

### 🎭 Vai trò (Roles)
- **Admin**: <PERSON>uản trị viên hệ thống
- **Teacher**: Giáo viên, có thể tạo và quản lý khóa học
- **Student**: Học viên, có thể tham gia khóa học

## API Endpoints

### Backend Routes
```
GET    /api/admin/users          - Lấy danh sách users
POST   /api/admin/users          - Tạo user mới
PUT    /api/admin/users/:id      - Cập nhật user
DELETE /api/admin/users/:id      - Xóa user
PUT    /api/admin/users/:id/role - Cập nhật role user
PUT    /api/admin/users/:id/status - Cập nhật trạng thái user
```

### Request/Response Examples

#### Tạo User Mới
```json
POST /api/admin/users
{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe",
  "role": "student"
}
```

#### Cập nhật User
```json
PUT /api/admin/users/1
{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "role": "teacher",
  "is_active": true
}
```

## Frontend Components

### 📁 Cấu trúc Files
```
frontend/src/
├── pages/Users.tsx              # Trang chính User Management
├── components/UserForm.tsx      # Form tạo/sửa user
├── services/userService.ts     # API service calls
├── hooks/useUsers.ts           # Custom hook quản lý state
└── types/index.ts              # TypeScript types
```

### 🎨 UI Components
- **Stats Cards**: Hiển thị thống kê tổng quan
- **User Table**: Bảng danh sách users với actions
- **User Form Modal**: Form tạo/chỉnh sửa user
- **Delete Confirmation**: Modal xác nhận xóa

## Bảo mật

### 🛡️ Backend Security
- **JWT Authentication**: Xác thực bằng token
- **Role-based Access**: Chỉ Admin mới truy cập được
- **Password Hashing**: Mật khẩu được hash bằng bcrypt
- **Input Validation**: Validate dữ liệu đầu vào
- **Prevent Self-deletion**: Admin không thể xóa chính mình

### 🔒 Frontend Security
- **Route Protection**: AdminRoute component bảo vệ route
- **Conditional Rendering**: Menu chỉ hiện với Admin
- **Error Handling**: Xử lý lỗi API một cách an toàn

## Cách sử dụng

### 1. Truy cập trang User Management
- Đăng nhập với tài khoản Admin
- Click vào "User Management" trong sidebar

### 2. Tạo User mới
- Click nút "Add User"
- Điền thông tin: Email, Password, Tên, Role
- Click "Create User"

### 3. Chỉnh sửa User
- Click icon "Edit" trong bảng users
- Cập nhật thông tin cần thiết
- Click "Update User"

### 4. Xóa User
- Click icon "Delete" trong bảng users
- Xác nhận xóa trong modal

### 5. Quản lý Role
- Sử dụng dropdown trong form để thay đổi role
- Roles: Admin, Teacher, Student

## Tài khoản mặc định

Khi chạy seeds, hệ thống tạo sẵn các tài khoản:

```
Admin: <EMAIL> / password
Teacher: <EMAIL> / teacher123
Student: <EMAIL> / student123
```

## Lưu ý kỹ thuật

### Database
- Sử dụng GORM với PostgreSQL
- Soft delete cho users
- Unique constraint trên email

### Frontend
- React với TypeScript
- Bootstrap 5 cho UI
- Axios cho API calls
- React Hook Form cho validation

### Error Handling
- Backend trả về error messages rõ ràng
- Frontend hiển thị errors trong UI
- Validation ở cả client và server side
