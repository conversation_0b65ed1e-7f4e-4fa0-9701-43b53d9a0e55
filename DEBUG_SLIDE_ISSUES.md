# Debug Slide Creation Issues

## Vấn đề cần debug

### 1. Lỗi order_index khi tạo slide
- Backend báo lỗi thiếu order_index
- Cần kiểm tra xem order_index có được tính đúng không

### 2. Navigation không quay lại Course Edit
- Save/Cancel từ SlideCreate không quay về Course Edit
- <PERSON><PERSON> thể do URL parameters không được đọc đúng

## Debug logs đã thêm

### SlideCreate.tsx
```typescript
// Debug URL parameters
console.log('SlideCreate URL params:', {
  lessonId,
  courseId,
  fromCourseEdit,
  allSearchParams: Object.fromEntries(searchParams.entries()),
  currentURL: window.location.href
});

// Debug existing slides
console.log('Existing slides:', existingSlides);

// Debug slide data
console.log('Creating slide with data:', slideData);

// Debug navigation
console.log('Navigation decision:', {
  fromCourseEdit,
  courseId,
  lesson: lesson?.id,
  lessonId
});

// Simplified navigation logic
const from = searchParams.get('from');
const courseIdParam = searchParams.get('courseId');
console.log('URL params check:', { from, courseIdParam });
```

### slideService.ts
```typescript
// Debug API call
console.log('Sending slide data to API:', dataToSend);
const response = await api.post<SlideResponse>('/slides', dataToSend);
console.log('API response:', response.data);
```

## Cách test debug

### 1. Test Navigation
1. Vào `/navigation-test`
2. Click "Test Navigation to SlideCreate with params"
3. Kiểm tra console logs:
   - URL parameters có đúng không
   - fromCourseEdit có true không
   - courseId có giá trị không

### 2. Test Slide Creation
1. Từ Course Edit → Create slide
2. Kiểm tra console logs:
   - Existing slides data
   - Order index calculation
   - Slide data gửi lên API
   - API response

### 3. Test Navigation Back
1. Từ SlideCreate → Save hoặc Cancel
2. Kiểm tra console logs:
   - Navigation decision logic
   - URL params check
   - Target URL

## Expected Debug Output

### Khi vào SlideCreate từ Course Edit:
```
SlideCreate URL params: {
  lessonId: "1",
  courseId: "1", 
  fromCourseEdit: true,
  allSearchParams: { from: "course-edit", courseId: "1" },
  currentURL: "http://localhost:5174/lessons/1/slides/create?from=course-edit&courseId=1"
}
```

### Khi tạo slide:
```
Existing slides: [
  { id: 1, order_index: 1, title: "Slide 1" },
  { id: 2, order_index: 2, title: "Slide 2" }
]

Creating slide with data: {
  lesson_id: 1,
  title: "New Slide",
  content: "Content...",
  order_index: 3
}

Sending slide data to API: {
  lesson_id: 1,
  title: "New Slide", 
  content: "Content...",
  order_index: 3
}
```

### Khi navigate back:
```
Navigation decision: {
  fromCourseEdit: true,
  courseId: "1",
  lesson: undefined,
  lessonId: "1"
}

URL params check: { from: "course-edit", courseIdParam: "1" }

Navigating to course edit: /courses/1/edit?tab=content
```

## Possible Issues & Solutions

### Issue 1: fromCourseEdit = false
**Nguyên nhân**: URL parameter không được truyền đúng từ CourseEdit
**Giải pháp**: Kiểm tra CourseEdit navigation code

### Issue 2: courseId = null
**Nguyên nhân**: courseId không được truyền hoặc đọc sai
**Giải pháp**: Kiểm tra searchParams.get('courseId')

### Issue 3: order_index = NaN hoặc undefined
**Nguyên nhân**: existingSlides empty hoặc order_index calculation sai
**Giải pháp**: 
```typescript
const nextOrderIndex = existingSlides.length > 0 
  ? Math.max(...existingSlides.map(slide => slide.order_index)) + 1 
  : 1;
```

### Issue 4: API error
**Nguyên nhân**: Backend validation fail
**Giải pháp**: Kiểm tra API payload và backend logs

## Test URLs

### Manual test URLs:
```
# Test SlideCreate with params
http://localhost:5174/lessons/1/slides/create?from=course-edit&courseId=1

# Test CourseEdit with tab
http://localhost:5174/courses/1/edit?tab=content

# Test navigation debug
http://localhost:5174/navigation-test
```

## Backend Debug

### Check backend logs for:
```
POST /api/slides
{
  "lesson_id": 1,
  "title": "Test",
  "content": "Content",
  "order_index": 1
}
```

### Expected backend response:
```json
{
  "slide": {
    "id": 3,
    "lesson_id": 1,
    "title": "Test",
    "content": "Content", 
    "order_index": 1,
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

## Quick Fixes

### Fix 1: Force navigation
```typescript
// In SlideCreate handleSave/handleCancel
window.location.href = `/courses/${courseIdParam}/edit?tab=content`;
```

### Fix 2: Hardcode order_index
```typescript
// Temporary fix
const nextOrderIndex = 1; // Always start with 1
```

### Fix 3: Debug API call
```typescript
// Add try-catch around API call
try {
  const response = await slideService.createSlide(slideData);
  console.log('Success:', response);
} catch (error) {
  console.error('API Error:', error.response?.data);
}
```

## Next Steps

1. **Open browser console** và vào test URLs
2. **Check debug logs** để xác định vấn đề
3. **Fix issues** dựa trên debug output
4. **Remove debug logs** sau khi fix xong

## Test Checklist

- [ ] URL parameters được đọc đúng
- [ ] fromCourseEdit = true khi từ Course Edit
- [ ] courseId có giá trị đúng
- [ ] order_index được tính đúng (>= 1)
- [ ] API call thành công
- [ ] Navigation quay về Course Edit tab content
- [ ] CourseEdit auto-switch to content tab

## Browser Console Commands

```javascript
// Check current URL params
new URLSearchParams(window.location.search)

// Check navigation state
window.history.state

// Force navigation
window.location.href = '/courses/1/edit?tab=content'
```
