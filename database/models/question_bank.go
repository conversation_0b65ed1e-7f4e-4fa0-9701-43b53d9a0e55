package models

import (
	"time"

	"gorm.io/gorm"
)

// QuestionBankCategory represents categories for organizing questions
type QuestionBankCategory struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"not null;unique"`
	Description string         `json:"description"`
	CourseID    *uint          `json:"course_id"` // Optional: link to specific course
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Course        Course         `json:"course,omitempty"`
	BankQuestions []BankQuestion `json:"bank_questions,omitempty" gorm:"foreignKey:CategoryID"`
}

// BankQuestion represents questions in the question bank (independent of quizzes)
type BankQuestion struct {
	ID           uint            `json:"id" gorm:"primaryKey"`
	CategoryID   uint            `json:"category_id" gorm:"not null"`
	QuestionText string          `json:"question_text" gorm:"type:text;not null"`
	QuestionType QuestionType    `json:"question_type" gorm:"not null"`
	ImageURL     string          `json:"image_url"`
	Points       float64         `json:"points" gorm:"default:1"`
	Difficulty   DifficultyLevel `json:"difficulty" gorm:"default:'normal'"`
	Tags         string          `json:"tags"` // Comma-separated tags for better search
	CreatedBy    uint            `json:"created_by" gorm:"not null"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	DeletedAt    gorm.DeletedAt  `json:"-" gorm:"index"`

	// Relationships
	Category             QuestionBankCategory  `json:"category,omitempty"`
	Creator              User                  `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
	Options              []BankOption          `json:"options,omitempty" gorm:"orderBy:order_index"`
	QuizAttemptQuestions []QuizAttemptQuestion `json:"quiz_attempt_questions,omitempty"`
}

// BankOption represents answer options for bank questions
type BankOption struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	BankQuestionID uint           `json:"bank_question_id" gorm:"not null"`
	OptionText     string         `json:"option_text" gorm:"not null"`
	IsCorrect      bool           `json:"is_correct" gorm:"default:false"`
	OrderIndex     int            `json:"order_index" gorm:"not null"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	BankQuestion BankQuestion `json:"bank_question,omitempty"`
}

// QuizRule represents rules for dynamically generating questions for a quiz
type QuizRule struct {
	ID           uint            `json:"id" gorm:"primaryKey"`
	QuizID       uint            `json:"quiz_id" gorm:"not null"`
	CategoryID   uint            `json:"category_id" gorm:"not null"`
	Difficulty   DifficultyLevel `json:"difficulty" gorm:"not null"`
	QuestionType QuestionType    `json:"question_type" gorm:"not null"`
	Percentage   float64         `json:"percentage" gorm:"not null"` // Percentage of total questions
	OrderIndex   int             `json:"order_index" gorm:"not null"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	DeletedAt    gorm.DeletedAt  `json:"-" gorm:"index"`

	// Relationships
	Quiz     Quiz                 `json:"quiz,omitempty"`
	Category QuestionBankCategory `json:"category,omitempty"`
}

// GetCalculatedCount returns the calculated number of questions based on quiz's total questions
func (qr *QuizRule) GetCalculatedCount(totalQuestions int) int {
	count := int(float64(totalQuestions) * qr.Percentage / 100.0)
	if count < 1 && qr.Percentage > 0 {
		count = 1 // Minimum 1 question if percentage > 0
	}
	return count
}

// QuizAttemptQuestion represents specific questions generated for a quiz attempt
type QuizAttemptQuestion struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	QuizAttemptID  uint           `json:"quiz_attempt_id" gorm:"not null"`
	BankQuestionID uint           `json:"bank_question_id" gorm:"not null"`
	OrderIndex     int            `json:"order_index" gorm:"not null"`
	Points         float64        `json:"points" gorm:"not null"` // Points for this question in this attempt
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	QuizAttempt  QuizAttempt  `json:"quiz_attempt,omitempty"`
	BankQuestion BankQuestion `json:"bank_question,omitempty"`
}
