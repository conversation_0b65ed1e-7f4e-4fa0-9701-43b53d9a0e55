package models

import (
	"time"

	"gorm.io/gorm"
)

type Course struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Title       string         `json:"title" gorm:"not null"`
	Description string         `json:"description"`
	Thumbnail   string         `json:"thumbnail"`
	CreatorID   uint           `json:"creator_id" gorm:"not null"`
	IsPublished bool           `json:"is_published" gorm:"default:false"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Creator     User         `json:"creator,omitempty"`
	Lessons     []Lesson     `json:"lessons,omitempty" gorm:"orderBy:order_index"`
	Quizzes     []Quiz       `json:"quizzes,omitempty"`
	Enrollments []Enrollment `json:"enrollments,omitempty"`
}

type Lesson struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	CourseID    uint           `json:"course_id" gorm:"not null"`
	Title       string         `json:"title" gorm:"not null"`
	Description string         `json:"description"`
	OrderIndex  int            `json:"order_index" gorm:"not null"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Course Course `json:"course,omitempty"`
	Slides []Slide `json:"slides,omitempty" gorm:"orderBy:order_index"`
}

type Slide struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	LessonID   uint           `json:"lesson_id" gorm:"not null"`
	Title      string         `json:"title"`
	Content    string         `json:"content" gorm:"type:text"`
	ImageURL   string         `json:"image_url"`
	OrderIndex int            `json:"order_index" gorm:"not null"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Lesson Lesson `json:"lesson,omitempty"`
}

type UserProgress struct {
	ID       uint `json:"id" gorm:"primaryKey"`
	UserID   uint `json:"user_id" gorm:"not null"`
	CourseID uint `json:"course_id" gorm:"not null"`
	LessonID uint `json:"lesson_id" gorm:"not null"`
	SlideID  uint `json:"slide_id" gorm:"not null"`
	
	IsCompleted bool      `json:"is_completed" gorm:"default:false"`
	CompletedAt time.Time `json:"completed_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Relationships
	User   User   `json:"user,omitempty"`
	Course Course `json:"course,omitempty"`
	Lesson Lesson `json:"lesson,omitempty"`
	Slide  Slide  `json:"slide,omitempty"`
}
