package models

import (
	"time"

	"gorm.io/gorm"
)

type UserRole string

const (
	RoleAdmin   UserRole = "admin"
	RoleTeacher UserRole = "teacher"
	RoleStudent UserRole = "student"
)

type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Email     string         `json:"email" gorm:"uniqueIndex;not null"`
	Password  string         `json:"-" gorm:"not null"`
	FirstName string         `json:"first_name" gorm:"not null"`
	LastName  string         `json:"last_name" gorm:"not null"`
	Role      UserRole       `json:"role" gorm:"type:varchar(20);default:'student'"`
	Avatar    string         `json:"avatar"`
	IsActive  bool           `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	CreatedCourses []Course     `json:"created_courses,omitempty" gorm:"foreignKey:CreatorID"`
	Enrollments    []Enrollment `json:"enrollments,omitempty"`
	QuizAttempts   []QuizAttempt `json:"quiz_attempts,omitempty"`
}

type Enrollment struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id" gorm:"not null"`
	CourseID   uint      `json:"course_id" gorm:"not null"`
	EnrolledAt time.Time `json:"enrolled_at" gorm:"autoCreateTime"`
	Progress   float64   `json:"progress" gorm:"default:0"`

	// Relationships
	User   User   `json:"user,omitempty"`
	Course Course `json:"course,omitempty"`
}
