package models

import (
	"time"

	"gorm.io/gorm"
)

type QuestionType string

const (
	QuestionTypeSingleChoice   QuestionType = "single_choice"
	QuestionTypeMultipleChoice QuestionType = "multiple_choice"
)

type DifficultyLevel string

const (
	DifficultyEasy   DifficultyLevel = "easy"
	DifficultyNormal DifficultyLevel = "normal"
	DifficultyHard   DifficultyLevel = "hard"
)

type Quiz struct {
	ID             uint            `json:"id" gorm:"primaryKey"`
	CourseID       uint            `json:"course_id" gorm:"not null"`
	Title          string          `json:"title" gorm:"not null"`
	Description    string          `json:"description"`
	TimeLimit      int             `json:"time_limit"` // in minutes, 0 means no limit
	PassScore      float64         `json:"pass_score" gorm:"default:70"`
	IsPublished    bool            `json:"is_published" gorm:"default:false"`
	Difficulty     DifficultyLevel `json:"difficulty" gorm:"default:'normal'"`
	CategoryID     *uint           `json:"category_id"`
	TotalQuestions int             `json:"total_questions" gorm:"default:10"` // Total number of questions to generate
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
	DeletedAt      gorm.DeletedAt  `json:"-" gorm:"index"`

	// Relationships
	Course       Course        `json:"course,omitempty"`
	Category     Category      `json:"category,omitempty"`
	Questions    []Question    `json:"questions,omitempty"`
	QuizAttempts []QuizAttempt `json:"quiz_attempts,omitempty"`
}

type Question struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	QuizID       uint           `json:"quiz_id" gorm:"not null"`
	QuestionText string         `json:"question_text" gorm:"type:text;not null"`
	QuestionType QuestionType   `json:"question_type" gorm:"not null"`
	ImageURL     string         `json:"image_url"`
	Points       float64        `json:"points" gorm:"default:1"`
	OrderIndex   int            `json:"order_index" gorm:"not null"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Quiz    Quiz     `json:"quiz,omitempty"`
	Options []Option `json:"options,omitempty" gorm:"orderBy:order_index"`
}

type Option struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	QuestionID uint           `json:"question_id" gorm:"not null"`
	OptionText string         `json:"option_text" gorm:"not null"`
	IsCorrect  bool           `json:"is_correct" gorm:"default:false"`
	OrderIndex int            `json:"order_index" gorm:"not null"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Question Question `json:"question,omitempty"`
}

type QuizAttempt struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	QuizID      uint      `json:"quiz_id" gorm:"not null"`
	Score       float64   `json:"score"`
	MaxScore    float64   `json:"max_score"`
	Percentage  float64   `json:"percentage"`
	IsPassed    bool      `json:"is_passed" gorm:"default:false"`
	StartedAt   time.Time `json:"started_at"`
	CompletedAt time.Time `json:"completed_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	// Relationships
	User    User     `json:"user,omitempty"`
	Quiz    Quiz     `json:"quiz,omitempty"`
	Answers []Answer `json:"answers,omitempty"`
}

type Answer struct {
	ID            uint `json:"id" gorm:"primaryKey"`
	QuizAttemptID uint `json:"quiz_attempt_id" gorm:"not null"`
	QuestionID    uint `json:"question_id" gorm:"not null"`
	OptionID      uint `json:"option_id" gorm:"not null"`

	// Relationships
	QuizAttempt QuizAttempt `json:"quiz_attempt,omitempty"`
	Question    Question    `json:"question,omitempty"`
	Option      Option      `json:"option,omitempty"`
}

type Category struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"not null;unique"`
	Description string         `json:"description"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Quizzes []Quiz `json:"quizzes,omitempty"`
}
