# Database Module

This module contains all database-related operations for the Etuto project, including migrations, seeding, and model definitions.

## Structure

```
database/
├── config/          # Database configuration
├── connection/      # Database connection utilities
├── migrations/      # Database migration logic
├── models/          # Database model definitions
├── seeds/           # Database seeding logic
├── utils/           # Database utilities (password hashing, etc.)
├── main.go          # CLI tool for database operations
├── go.mod           # Go module definition
└── README.md        # This file
```

## Usage

### Prerequisites

1. Make sure PostgreSQL is running
2. Set up your environment variables (DATABASE_URL, etc.) in a `.env` file in the project root

### Commands

Run database operations using the CLI tool:

```bash
# Run migrations only
go run database/main.go migrate

# Run seeds only (requires migrations to be run first)
go run database/main.go seed

# Run both migrations and seeds
go run database/main.go setup

# Reset database (WARNING: This will delete all data)
go run database/main.go reset
```

### Environment Variables

The following environment variables are used:

- `DATABASE_URL`: PostgreSQL connection string (default: `postgres://postgres:password@localhost:5432/etuto_dev?sslmode=disable`)
- `JWT_SECRET`: Secret key for JWT tokens (default: `your-secret-key-change-in-production`)
- `PORT`: Server port (default: `8080`)

### Models

The following models are defined:

- **User**: User accounts with roles (admin, teacher, student)
- **Course**: Learning courses created by teachers
- **Lesson**: Individual lessons within courses
- **Slide**: Content slides within lessons
- **Quiz**: Quizzes associated with courses
- **Question**: Questions within quizzes
- **Option**: Answer options for questions
- **Enrollment**: Student enrollments in courses
- **UserProgress**: User progress tracking through slides
- **QuizAttempt**: User attempts at quizzes
- **Answer**: User answers to quiz questions

### Default Data

When running seeds, the following default data is created:

**Users:**
- Admin: `<EMAIL>` / `password`
- Teacher: `<EMAIL>` / `teacher123`
- Student: `<EMAIL>` / `student123`

**Courses:**
- Introduction to Web Development (Published)
- React Fundamentals (Published)
- Database Design (Draft)

**Sample lessons, slides, and quizzes** are also created for the courses.

## Integration with Backend

The backend can use this database module by importing the connection and models:

```go
import (
    "etuto-database/connection"
    "etuto-database/models"
)

// In your backend initialization
func main() {
    if err := connection.Connect(); err != nil {
        log.Fatal("Failed to connect to database:", err)
    }
    
    db := connection.GetDB()
    // Use db for your operations
}
```

## Development

### Adding New Models

1. Create the model in the appropriate file under `models/`
2. Add the model to the migration list in `migrations/migrate.go`
3. Update seeds if necessary

### Adding New Seeds

1. Add seed functions to the appropriate file under `seeds/`
2. Call the function from `seeds/seed.go`

### Running Tests

```bash
cd database
go test ./...
```
