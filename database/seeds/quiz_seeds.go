package seeds

import (
	"etuto-database/models"

	"gorm.io/gorm"
)

func seedQuizzes(db *gorm.DB, courses []models.Course, categories []models.Category) ([]models.Quiz, error) {
	if len(courses) == 0 || len(categories) == 0 {
		return nil, nil
	}

	var createdQuizzes []models.Quiz

	// Find categories by name for easier reference
	categoryMap := make(map[string]models.Category)
	for _, cat := range categories {
		categoryMap[cat.Name] = cat
	}

	course1 := courses[0] // Introduction to Web Development

	// Create quizzes for different difficulty levels and categories
	quizData := []struct {
		Title        string
		Description  string
		Difficulty   models.DifficultyLevel
		CategoryName string
		TimeLimit    int
		PassScore    float64
	}{
		{
			Title:        "HTML Basics - Easy",
			Description:  "Basic HTML concepts for beginners",
			Difficulty:   models.DifficultyEasy,
			CategoryName: "Web Development",
			TimeLimit:    10,
			PassScore:    60,
		},
		{
			Title:        "HTML Intermediate",
			Description:  "Intermediate HTML concepts and best practices",
			Difficulty:   models.DifficultyNormal,
			CategoryName: "Web Development",
			TimeLimit:    15,
			PassScore:    70,
		},
		{
			Title:        "HTML Advanced",
			Description:  "Advanced HTML5 features and semantic markup",
			Difficulty:   models.DifficultyHard,
			CategoryName: "Web Development",
			TimeLimit:    20,
			PassScore:    80,
		},
		{
			Title:        "Programming Basics - Easy",
			Description:  "Basic programming concepts for beginners",
			Difficulty:   models.DifficultyEasy,
			CategoryName: "Programming",
			TimeLimit:    15,
			PassScore:    60,
		},
		{
			Title:        "Programming Intermediate",
			Description:  "Intermediate programming concepts and algorithms",
			Difficulty:   models.DifficultyNormal,
			CategoryName: "Programming",
			TimeLimit:    20,
			PassScore:    70,
		},
		{
			Title:        "Programming Advanced",
			Description:  "Advanced programming patterns and optimization",
			Difficulty:   models.DifficultyHard,
			CategoryName: "Programming",
			TimeLimit:    25,
			PassScore:    80,
		},
		{
			Title:        "Database Basics - Easy",
			Description:  "Basic database concepts and SQL",
			Difficulty:   models.DifficultyEasy,
			CategoryName: "Database",
			TimeLimit:    15,
			PassScore:    60,
		},
		{
			Title:        "Database Intermediate",
			Description:  "Intermediate database design and optimization",
			Difficulty:   models.DifficultyNormal,
			CategoryName: "Database",
			TimeLimit:    20,
			PassScore:    70,
		},
		{
			Title:        "Database Advanced",
			Description:  "Advanced database administration and performance",
			Difficulty:   models.DifficultyHard,
			CategoryName: "Database",
			TimeLimit:    25,
			PassScore:    80,
		},
		{
			Title:        "An toàn thông tin",
			Description:  "Chương trình nâng cao nhận thức An toàn thông tin và Phòng chống thất thoát dữ liệu	",
			Difficulty:   models.DifficultyHard,
			CategoryName: "Cybersecurity",
			TimeLimit:    25,
			PassScore:    80,
		},
	}

	// Create quizzes and their questions
	for _, quizInfo := range quizData {
		category, exists := categoryMap[quizInfo.CategoryName]
		if !exists {
			continue
		}

		var quiz models.Quiz
		if err := db.Where("course_id = ? AND title = ?", course1.ID, quizInfo.Title).First(&quiz).Error; err != nil {
			quiz = models.Quiz{
				CourseID:    course1.ID,
				Title:       quizInfo.Title,
				Description: quizInfo.Description,
				TimeLimit:   quizInfo.TimeLimit,
				PassScore:   quizInfo.PassScore,
				IsPublished: true,
				Difficulty:  quizInfo.Difficulty,
				CategoryID:  &category.ID,
			}
			if err := db.Create(&quiz).Error; err != nil {
				return nil, err
			}
		}

		createdQuizzes = append(createdQuizzes, quiz)

		// Create questions based on difficulty level and category
		if err := createQuestionsForQuiz(db, quiz, quizInfo.Difficulty, category.Name); err != nil {
			return nil, err
		}
	}

	return createdQuizzes, nil
}

func createQuestionsForQuiz(db *gorm.DB, quiz models.Quiz, difficulty models.DifficultyLevel, categoryName string) error {
	var questions []QuestionData

	// Define questions based on category and difficulty level
	switch categoryName {
	case "Web Development":
		questions = getWebDevelopmentQuestions(difficulty)
	case "Programming":
		questions = getProgrammingQuestions(difficulty)
	case "Database":
		questions = getDatabaseQuestions(difficulty)
	case "Cybersecurity":
		questions = getCybersecurityQuestions(difficulty)
	default:
		// questions = getWebDevelopmentQuestions(difficulty) // fallback
	}

	// Create questions and options
	for i, questionData := range questions {
		var question models.Question
		questionText := questionData.Text
		if err := db.Where("quiz_id = ? AND question_text = ?", quiz.ID, questionText).First(&question).Error; err != nil {
			question = models.Question{
				QuizID:       quiz.ID,
				QuestionText: questionText,
				QuestionType: questionData.Type,
				Points:       questionData.Points,
				OrderIndex:   i + 1,
			}
			if err := db.Create(&question).Error; err != nil {
				return err
			}

			// Create options for the question
			for j, optionData := range questionData.Options {
				option := models.Option{
					QuestionID: question.ID,
					OptionText: optionData.Text,
					IsCorrect:  optionData.IsCorrect,
					OrderIndex: j + 1,
				}
				if err := db.Create(&option).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
}

type QuestionData struct {
	Text    string
	Type    models.QuestionType
	Points  float64
	Options []struct {
		Text      string
		IsCorrect bool
	}
}

func getWebDevelopmentQuestions(difficulty models.DifficultyLevel) []QuestionData {
	switch difficulty {
	case models.DifficultyEasy:
		return []QuestionData{
			{
				Text:   "What does HTML stand for?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 1,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"HyperText Markup Language", true},
					{"High Tech Modern Language", false},
					{"Home Tool Markup Language", false},
					{"Hyperlink and Text Markup Language", false},
				},
			},
			{
				Text:   "Which tag is used to create a paragraph in HTML?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 1,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"<p>", true},
					{"<paragraph>", false},
					{"<para>", false},
					{"<text>", false},
				},
			},
		}
	case models.DifficultyNormal:
		return []QuestionData{
			{
				Text:   "Which of the following are semantic HTML5 elements?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"<header>", true},
					{"<div>", false},
					{"<section>", true},
					{"<span>", false},
				},
			},
			{
				Text:   "What is the correct way to include CSS in HTML?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 1,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"<link rel=\"stylesheet\" href=\"style.css\">", true},
					{"<css src=\"style.css\">", false},
					{"<style src=\"style.css\">", false},
					{"<include css=\"style.css\">", false},
				},
			},
		}
	case models.DifficultyHard:
		return []QuestionData{
			{
				Text:   "Which HTML5 attributes are used for accessibility?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 3,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"aria-label", true},
					{"role", true},
					{"class", false},
					{"alt", true},
				},
			},
			{
				Text:   "What is the purpose of the 'defer' attribute in script tags?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"Delays script execution until HTML parsing is complete", true},
					{"Loads script asynchronously", false},
					{"Prevents script from executing", false},
					{"Caches the script for faster loading", false},
				},
			},
		}
	}
	return nil
}

func getProgrammingQuestions(difficulty models.DifficultyLevel) []QuestionData {
	switch difficulty {
	case models.DifficultyEasy:
		return []QuestionData{
			{
				Text:   "What is a variable in programming?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 1,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"A container for storing data values", true},
					{"A type of loop", false},
					{"A function", false},
					{"A programming language", false},
				},
			},
			{
				Text:   "Which of the following are programming languages?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"Python", true},
					{"HTML", false},
					{"Java", true},
					{"CSS", false},
				},
			},
		}
	case models.DifficultyNormal:
		return []QuestionData{
			{
				Text:   "What is the time complexity of binary search?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"O(log n)", true},
					{"O(n)", false},
					{"O(n²)", false},
					{"O(1)", false},
				},
			},
			{
				Text:   "Which data structures use LIFO principle?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"Stack", true},
					{"Queue", false},
					{"Call Stack", true},
					{"Array", false},
				},
			},
		}
	case models.DifficultyHard:
		return []QuestionData{
			{
				Text:   "Which design patterns are creational patterns?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 3,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"Singleton", true},
					{"Observer", false},
					{"Factory", true},
					{"Strategy", false},
				},
			},
			{
				Text:   "What is the space complexity of merge sort?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 3,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"O(n)", true},
					{"O(log n)", false},
					{"O(1)", false},
					{"O(n log n)", false},
				},
			},
		}
	}
	return nil
}

func getDatabaseQuestions(difficulty models.DifficultyLevel) []QuestionData {
	switch difficulty {
	case models.DifficultyEasy:
		return []QuestionData{
			{
				Text:   "What does SQL stand for?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 1,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"Structured Query Language", true},
					{"Simple Query Language", false},
					{"Standard Query Language", false},
					{"System Query Language", false},
				},
			},
			{
				Text:   "Which SQL commands are used for data manipulation?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"INSERT", true},
					{"CREATE", false},
					{"UPDATE", true},
					{"DROP", false},
				},
			},
		}
	case models.DifficultyNormal:
		return []QuestionData{
			{
				Text:   "What is a foreign key?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"A field that refers to the primary key of another table", true},
					{"A unique identifier for a table", false},
					{"An index on a table", false},
					{"A constraint that prevents null values", false},
				},
			},
			{
				Text:   "Which normal forms eliminate partial dependencies?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 2,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"2NF", true},
					{"1NF", false},
					{"3NF", true},
					{"BCNF", true},
				},
			},
		}
	case models.DifficultyHard:
		return []QuestionData{
			{
				Text:   "Which isolation levels prevent dirty reads?",
				Type:   models.QuestionTypeMultipleChoice,
				Points: 3,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"READ COMMITTED", true},
					{"READ UNCOMMITTED", false},
					{"REPEATABLE READ", true},
					{"SERIALIZABLE", true},
				},
			},
			{
				Text:   "What is the purpose of database sharding?",
				Type:   models.QuestionTypeSingleChoice,
				Points: 3,
				Options: []struct {
					Text      string
					IsCorrect bool
				}{
					{"Horizontal partitioning to distribute data across multiple servers", true},
					{"Creating backup copies of data", false},
					{"Encrypting sensitive data", false},
					{"Compressing data to save space", false},
				},
			},
		}
	}
	return nil
}

func getCybersecurityQuestions(difficulty models.DifficultyLevel) []QuestionData {

	return []QuestionData{
		{
			Text:   "VPS có được quyền kiểm tra thiết bị di động cầm tay của cá nhân không?",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Không được", false},
				{"Được", false},
				{"Được quyền kiểm tra trước sự chứng kiến của cá nhân", true},
				{"Tất cả phương án trên đều sai", false},
			},
		},
		{
			Text:   "Hành vi nào sau đây bị nghiêm cấm trong quá trình sử dụng ổ chung của VPS",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Cấp quyền cho đối tác truy cập vào Ổ chung của VPS để chia sẻ thông tin trong quá trình triển khai dự án", false},
				{"Tạo thư mục chung chia sẻ dữ liệu giữa đơn vị mình (ví dụ khối back) với TVĐT&TVTC để chai sẻ thông tin", false},
				{"Upload dữ liệu lên thư mục chung của đơn vị để lưu trữ và chia sẻ thông tin trong đơn vị", false},
				{"Cả 1 &2", true},
			},
		},
		{
			Text:   "Đơn vị có nhu cầu về sử dụng dữ liệu từ đơn vị sở hữu dữ liệu, cần được sự phê duyệt của ai",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Trưởng đơn vị có nhu cầu. Phòng Kiểm soát nội bộ. Giám đốc CNTT. Trưởng đơn vị sở hữu dữ liệu.", false},
				{"Trưởng đơn vị có nhu cầu. Phòng Kiểm soát nội bộ. Trưởng đơn vị sở hữu dữ liệu..", false},
				{"Trưởng đơn vị có nhu cầu. Phòng Kiểm soát nội bộ. Trưởng đơn vị sở hữu dữ liệu. Các tài liệu tuyệt mật cần có thêm phê duyệt của TGĐ", false},
				{"Trưởng đơn vị có nhu cầu. Phòng Kiểm soát nội bộ. Giám đốc CNTT. Trưởng đơn vị sở hữu dữ liệu. Các tài liệu tuyệt mật cần có thêm phê duyệt của TGĐ", true},
			},
		},
		{
			Text:   "Khi cung cấp dữ liệu cho Phòng khác, các phương thức cung cấp dữ liệu nào sau đây được phép sử dụng",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Gửi qua email tới người yêu cầu, file dữ liệu cần được mã hóa/đặt mật khẩu", false},
				{"Gửi qua hệ thống dataroom", false},
				{"Gửi qua hệ thống cung cấp dữ liệu khác phù hợp đã được BP bảo mật đánh giá an toàn", false},
				{"Tất cả các đáp án trên", true},
			},
		},
		{
			Text:   "Ai có thẩm quyền phê duyệt việc chuyển đổi nhóm thông tin sang Công khai",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"TGĐ hoặc Người được TGĐ ủy quyền", true},
				{"Trưởng đơn vị hoặc người được TĐV ủy quyền", false},
				{"Trưởng phòng KSNB", false},
				{"Tất cả CBNV VPS", false},
			},
		},
		{
			Text:   "Stealer malware là gì?",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Phần mềm giúp bạn bảo vệ mật khẩu", false},
				{"Phần mềm gián điệp đánh cắp thông tin nhạy cảm", true},
				{"Phần mềm chống virus", false},
				{"Phần mềm tự động cập nhật", false},
			},
		},
		{
			Text:   "Công cụ phish alert dùng để làm gì",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Ngăn chặn mail phishing", false},
				{"Hỗ trợ người dùng report mail phishing tới BP Bảo mật", true},
				{"Hỗ trợ quét vi rút cho email", false},
				{"Cảnh báo mail phishing", false},
			},
		},
		{
			Text:   "Khi nhận được email có chứa file đính kèm từ địa chỉ lạ:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Mở file đính kèm để xem nội dung có liên quan tới mình không", false},
				{"Lưu file đính kèm về máy xem phần mềm Antivirus có cảnh báo gì không, nếu không thì mở file", false},
				{"Không mở file đính kèm vì nội dung email không liên quan tới bản thân", false},
				{"Không mở file đính kèm vì có khả năng là email spam hoặc email chứa mã độc, báo cáo dấu hiệu về bộ phận IT ngay khi nhận được email", true},
			},
		},
		{
			Text:   "Trong quá trình sử dụng thư điện tử VPS, hành động nào sau đây bị nghiêm cấm",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Sử dụng địa chỉ thư điện tử của VPS để đăng nhập vào các diễn đàn (forum) để bình luận gây war", false},
				{"Truy cập vào hòm thư của đồng nghiệp do đồng nghiệp nhờ kiểm tra hộ email khách hàng gửi đến", false},
				{"Gửi thông tin Tuyệt mật, bảo mât, nội bộ của VPS ra bên ngoài mà không đăng ký trước", false},
				{"Tất cả các hành động trên đều bị nghiêm cấm", true},
			},
		},
		{
			Text:   "Thông tin, nội dung trên email cá nhân @vps là sở hữu của:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Cá nhân", false},
				{"Trưởng bộ phận", false},
				{"VPS", true},
				{"Tất cả các phương án trên", false},
			},
		},
		{
			Text:   "Trong các trường hợp nào dưới đây nhân sự sẽ được tạo mới tài khoản thư điện tử VPS",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Nhân sự mới khi Onboard", false},
				{"CBNV nghỉ việc quay lại làm việc", false},
				{"CBNV thuyên chuyển công tác giữa các đơn vị trong VPS", false},
				{"1 và 2", true},
			},
		},
		{
			Text:   "Máy tính VPS được phép kết nối Internet sử dụng các cách thức nào dưới đây?",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Lắp đặt bổ sung card wifi hoặc USB wifi để kết nối wifi vào Internet", false},
				{"Lắp USB DCOM và kết nối vào Internet sử dụng SIM data", false},
				{"Kết nối với điện thoại di động và sử dụng chức năng chia sẻ Internet của điện thoại để vào Internet", false},
				{"Cả 3 phương án trên đều không được phép", true},
			},
		},
		{
			Text:   "CBNV VPS được phép truy cập vào các địa chỉ Internet nào ?",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Toàn bộ các website VPS, các website nhóm chứng khoán, tài chính, ngân hàng", false},
				{"Trang tìm kiếm Google", false},
				{"Địa chỉ truy cập Internet đã được BP Bảo mật đánh giá là an toàn", false},
				{"Cả 3 phương án trên đều đúng", true},
			},
		},
		{
			Text:   "Câu hỏi tình huống:\nNhân viên A gia nhập VPS từ tháng 8 với vị trí CTV.Tháng 10, nhân viên A chuyển từ CTV lên NV.\nVậy rank tháng 10 của nhân viên A là bao nhiêu",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Rank 3", false},
				{"Rank 2", false},
				{"Tham gia đào tạo và khởi tạo theo kết quả đào tạo", true},
				{"Không được tính rank ATTT", false},
			},
		},
		{
			Text:   "Câu hỏi tình huống:\nNhân viên A là nhân viên mới. Tháng này nhân viên A đã tham gia khóa học đào tạo nhận thức ATTT và hoàn thành bài thi với điểm số 90%\nVậy rank ATTT của nhân viên A được khởi tạo là bao nhiêu",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Chưa được khởi tạo", false},
				{"Rank 3", true},
				{"Rank 2", false},
				{"Rank 4", false},
			},
		},
		{
			Text:   "Câu hỏi tình huống:\nNhân viên A có rank ATTT tháng 8 là 4.\nTrong tháng 9, nhân viên A nhận được 3 email phishing và đã report cả 3 mail\nVậy rank tháng 9 của nhân viên A là bao nhiêu:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Rank 4", false},
				{"Rank 4.5, làm tròn lên 5", true},
				{"Rank 5", false},
				{"Rank 5.5, làm tròn lên 6", false},
			},
		},
		{
			Text:   "Khi nào cá nhân được cấp tài khoản phục vụ công việc:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Khi cá nhân đã ký cam kêt tuân thủ Quy chế bảo mật thông tin", true},
				{"Khi cá nhân có nhu cầu để phục vụ công việc", false},
				{"Khi cá nhân đã ký hợp đồng lao động", false},
				{"Không phương án nào đúng", false},
			},
		},
		{
			Text:   "CBNV chỉ được cấp quyền truy cập trên hệ thống của VPS khi đáp ứng các điều kiện sau",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Đăng ký hợp lệ theo quy trình và được Trưởng đơn vị và KSNB phê duyệt", false},
				{"Khi được trưởng đơn vị phê duyệt", false},
				{"Mặc định được cấp quyền truy cập tương đương với các CBNV khác cùng vị trí, không cần phải đăng ký và xin phê duyệt", false},
				{"Đáp án 2 và 3", true},
			},
		},
		{
			Text:   "Để đáp ứng yêu cầu đặt mật khẩu mạnh của VPS, CBNV khi đặt mật khẩu cần đáp ứng yêu cầu nào sau đây",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Đặt mật khẩu dài từ 8 ký tự trở lên", false},
				{"Mật khẩu chứa tối thiểu 1 ký tự số, 1 ký tự đặc biệt, 1 ký tự chữ thường và 1 ký tự chữ hoa", false},
				{"Không chứa các ký tự cấm như Vps, abc, password, hay cụm thời gian năm,...", false},
				{"Tất cả các yêu cầu trên", true},
			},
		},
		{
			Text:   "Mật khẩu nào sau đây đáp ứng yêu cầu đặt mặt khẩu mạnh của VPS",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Vps@123456", false},
				{"Anhnv@15081998", false},
				{"Abc@2023", false},
				{"Zu080%weof4!", true},
			},
		},
		{
			Text:   "Dữ liệu tạo ra trên máy tính do VPS cung cấp là tài sản của:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Cá nhân tạo ra dữ liệu", false},
				{"Trưởng bộ phận cá nhân tạo ra dữ liệu", false},
				{"VPS", true},
				{"Giám đốc IT", false},
			},
		},
		{
			Text:   "Khi sử dụng máy tính VPS cấp phát, CBNV phải:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Chỉ sử dụng máy tính cá nhân cho mục đích và yêu cầu công việc.", false},
				{"Không chia sẻ tệp tin/thư mục trên máy tính nhân viên.", false},
				{"Không dùng Internet để xem, lưu trữ, phát tán hình ảnh, văn hóa phẩm đồi trụy.", false},
				{"Đáp án 1 và 2.", true},
			},
		},
		{
			Text:   "An toàn thông tin tại VPS là:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Chính sách/ Quy định/ Quy chế ANTT", false},
				{"Hệ thống và cơ chế giám sát vật lý/ con người", false},
				{"Hệ thống và cơ chế giám sát công nghệ", false},
				{"Tất cả các phương án trên", true},
			},
		},
		{
			Text:   "Trong hợp đồng, thỏa thuận ký kết với đối tác cho phép đối tác kết nối tới hệ thống thông tin VPS, cung cấp triển khai các ứng dụng cho VPS yêu cầu cần phải có các thông tin:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Các điều khoản, nghĩa vụ của đối tác về bảo mật, cam kết không tiết lộ thông tin", false},
				{"Trách nhiệm xử lý các lỗ hổng bảo mật phần mềm", false},
				{"Điều khoản xử phạt trong trường hợp vi phạm quy định đã ký kết", false},
				{"Tất cả phương án trên đều đúng", true},
			},
		},
		{
			Text:   "Tính toàn vẹn của thông tin là gì",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Đảm bảo việc hạn chế truy cập và tiết lộ thông tin tới người được phép, bao gồm các phương thức bảo vệ thông tin riêng tư cá nhân và các thông tin sở hữu trí tuệ.", false},
				{"Bảo vệ thông tin tránh thay đổi, hủy trái phép, bao gồm đảm bảo tính xác thực và chống chối bỏ.", true},
				{"Đảm bảo truy cập và sử dụng thông tin một cách tin cậy và kịp thời", false},
				{"Không đáp án nào đúng", false},
			},
		},
		{
			Text:   "Khi ghi nhận vi phạm, hệ thống DLP cảnh báo cho user như thế nào?",
			Type:   models.QuestionTypeMultipleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Gửi SMS", false},
				{"Cảnh báo qua pop-up trên màn hình", true},
				{"Cảnh báo qua email", true},
				{"Không hiển thị cảnh báo", false},
			},
		},
		{
			Text:   "Khi bị vi phạm, người dùng cần thực hiện giải trình trong vòng mấy giờ?",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"4 giờ", true},
				{"6 giờ", false},
				{"8 giờ", false},
				{"12 giờ", false},
			},
		},
		{
			Text:   "Thiết bị ngoại vi nào được phép cắm vào mạng nội bộ  VPS:",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Thiết bị phát sóng wifi", false},
				{"Thiết bị thu sóng wifi", false},
				{"Các thiết bị do BP IT được phê duyệt, lắp đặt", true},
				{"Cả 3 phương án trên đều đúng", false},
			},
		},
		{
			Text:   "Trường hợp tài khoản đăng nhập trên máy trạm của CBNV đăng nhập sai nhiều lần và bị tạm khóa, CBNV cần làm gì để mở khóa tài khoản",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Gửi yêu cầu đến Phòng IT để mở tài khoản khác", false},
				{"Tài khoản sẽ tự động mở khóa sau 5 phút, CBNV không cần làm gì.", false},
				{"CBNV liên hệ với Bộ phận ITHelp để được hỗ trợ mở khóa ngay.", false},
				{"Phương án 2 hoặc 3", true},
			},
		},
		{
			Text:   "Hành vi nào sau đây bị nghiêm cấm khi sử dụng thiết bị cá nhân kết nối vào hệ thống CNTT của VPS",
			Type:   models.QuestionTypeSingleChoice,
			Points: 1,
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Sử dụng WIFI miễn phí tại các quán café, trung tâm thương mai", false},
				{"Tải, cài đặt các phần mềm Crack, không có bản quyền trên thiết bị cá nhân", false},
				{"Làm việc ở nơi công cộng như quán café,  trung tâm thương mại. Màn hình máy tính không được che chắn, người khác có thể dễ dàng xem được nội dung trên màn hình", false},
				{"Tất cả các đáp án trên", true},
			},
		},
	}
}
