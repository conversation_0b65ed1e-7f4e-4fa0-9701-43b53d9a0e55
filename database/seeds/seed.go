package seeds

import (
	"fmt"
	"log"

	"etuto-database/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func RunSeeds() error {
	// Load config
	cfg := config.GetConfig()

	// Connect to database
	db, err := gorm.Open(postgres.Open(cfg.DatabaseURL), &gorm.Config{})
	if err != nil {
		return err
	}

	log.Println("Starting database seeding...")

	// Seed users
	teacher, student, err := seedUsers(db)
	if err != nil {
		return err
	}
	fmt.Printf("Teacher: %v\n", teacher)
	fmt.Printf("Student: %v\n", student)

	// Seed courses
	courses, err := seedCourses(db, teacher)
	if err != nil {
		return err
	}

	// Seed lessons and slides
	if err := seedLessonsAndSlides(db, courses); err != nil {
		return err
	}

	// Seed categories
	categories, err := seedCategories(db)
	if err != nil {
		return err
	}

	// Seed quizzes
	quizzes, err := seedQuizzes(db, courses, categories)
	if err != nil {
		return err
	}

	// Seed question bank
	if err := seedQuestionBank(db, courses, teacher); err != nil {
		return err
	}

	// Seed quiz rules
	if err := seedQuizRules(db, quizzes); err != nil {
		return err
	}

	log.Println("Sample data created successfully!")
	log.Println("Users created:")
	log.Println("- Admin: <EMAIL> / password")
	log.Println("- Teacher: <EMAIL> / teacher123")
	log.Println("- Student: <EMAIL> / student123")
	log.Println("Courses created:")
	log.Println("- Introduction to Web Development (Published)")
	log.Println("- React Fundamentals (Published)")
	log.Println("- Database Design (Draft)")

	return nil
}
