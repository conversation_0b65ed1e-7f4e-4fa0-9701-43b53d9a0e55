package seeds

import (
	"etuto-database/models"

	"gorm.io/gorm"
)

func seedQuestionBank(db *gorm.DB, courses []models.Course, teacher models.User) error {
	if len(courses) == 0 {
		return nil
	}

	course1 := courses[0] // Introduction to Web Development

	// Create Question Bank Categories
	categories := []struct {
		Name        string
		Description string
	}{
		{
			Name:        "HTML Fundamentals",
			Description: "Basic HTML concepts and syntax",
		},
		{
			Name:        "CSS Styling",
			Description: "CSS properties, selectors, and layout",
		},
		{
			Name:        "JavaScript Basics",
			Description: "JavaScript fundamentals and DOM manipulation",
		},
		{
			Name:        "Programming Logic",
			Description: "Basic programming concepts and algorithms",
		},
		{
			Name:        "Database Concepts",
			Description: "Database design and SQL fundamentals",
		},
	}

	var createdCategories []models.QuestionBankCategory
	for _, catData := range categories {
		var category models.QuestionBankCategory
		if err := db.Where("name = ? AND course_id = ?", catData.Name, course1.ID).First(&category).Error; err != nil {
			category = models.QuestionBankCategory{
				Name:        catData.Name,
				Description: catData.Description,
				CourseID:    &course1.ID,
			}
			if err := db.Create(&category).Error; err != nil {
				return err
			}
		}
		createdCategories = append(createdCategories, category)
	}

	// Create Bank Questions for each category
	for _, category := range createdCategories {
		if err := createBankQuestionsForCategory(db, category, teacher.ID); err != nil {
			return err
		}
	}

	return nil
}

func createBankQuestionsForCategory(db *gorm.DB, category models.QuestionBankCategory, createdBy uint) error {
	var questions []BankQuestionData

	switch category.Name {
	case "HTML Fundamentals":
		questions = getHTMLBankQuestions()
	case "CSS Styling":
		questions = getCSSBankQuestions()
	case "JavaScript Basics":
		questions = getJavaScriptBankQuestions()
	case "Programming Logic":
		questions = getProgrammingLogicBankQuestions()
	case "Database Concepts":
		questions = getDatabaseBankQuestions()
	}

	for _, questionData := range questions {
		var bankQuestion models.BankQuestion
		if err := db.Where("category_id = ? AND question_text = ?", category.ID, questionData.Text).First(&bankQuestion).Error; err != nil {
			bankQuestion = models.BankQuestion{
				CategoryID:   category.ID,
				QuestionText: questionData.Text,
				QuestionType: questionData.Type,
				Points:       questionData.Points,
				Difficulty:   questionData.Difficulty,
				Tags:         questionData.Tags,
				CreatedBy:    createdBy,
			}
			if err := db.Create(&bankQuestion).Error; err != nil {
				return err
			}

			// Create options for the bank question
			for j, optionData := range questionData.Options {
				option := models.BankOption{
					BankQuestionID: bankQuestion.ID,
					OptionText:     optionData.Text,
					IsCorrect:      optionData.IsCorrect,
					OrderIndex:     j + 1,
				}
				if err := db.Create(&option).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
}

type BankQuestionData struct {
	Text       string
	Type       models.QuestionType
	Points     float64
	Difficulty models.DifficultyLevel
	Tags       string
	Options    []struct {
		Text      string
		IsCorrect bool
	}
}

func getHTMLBankQuestions() []BankQuestionData {
	return []BankQuestionData{
		// Easy questions (10 questions)
		{
			Text:       "What does HTML stand for?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,basics,acronym",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"HyperText Markup Language", true},
				{"High Tech Modern Language", false},
				{"Home Tool Markup Language", false},
				{"Hyperlink and Text Markup Language", false},
			},
		},
		{
			Text:       "Which tag is used to create a paragraph?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,tags,paragraph",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<p>", true},
				{"<paragraph>", false},
				{"<para>", false},
				{"<text>", false},
			},
		},
		{
			Text:       "Which HTML tags are used for headings?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,headings,tags",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<h1>", true},
				{"<h3>", true},
				{"<header>", false},
				{"<h6>", true},
			},
		},
		{
			Text:       "What is the correct HTML tag for creating a line break?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,tags,linebreak",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<br>", true},
				{"<break>", false},
				{"<lb>", false},
				{"<newline>", false},
			},
		},
		{
			Text:       "Which tag is used to create a hyperlink?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,links,anchor",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<a>", true},
				{"<link>", false},
				{"<href>", false},
				{"<url>", false},
			},
		},
		{
			Text:       "What is the correct HTML tag for inserting an image?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,images,media",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<img>", true},
				{"<image>", false},
				{"<picture>", false},
				{"<photo>", false},
			},
		},
		{
			Text:       "Which HTML tags are used for creating lists?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,lists,structure",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<ul>", true},
				{"<ol>", true},
				{"<list>", false},
				{"<li>", true},
			},
		},
		{
			Text:       "What does the <title> tag define?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,head,title",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"The title of the document shown in browser tab", true},
				{"A heading on the page", false},
				{"The main content title", false},
				{"A tooltip text", false},
			},
		},
		{
			Text:       "Which tag is used to create a table?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,tables,structure",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<table>", true},
				{"<tab>", false},
				{"<grid>", false},
				{"<data>", false},
			},
		},
		{
			Text:       "What is the correct HTML tag for the largest heading?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "html,headings,hierarchy",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<h1>", true},
				{"<h6>", false},
				{"<heading>", false},
				{"<header>", false},
			},
		},

		// Normal questions (10 questions)
		{
			Text:       "Which HTML5 elements are semantic?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html5,semantic,elements",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<header>", true},
				{"<div>", false},
				{"<section>", true},
				{"<span>", false},
			},
		},
		{
			Text:       "What is the correct way to include CSS in HTML?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html,css,linking",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<link rel=\"stylesheet\" href=\"style.css\">", true},
				{"<css src=\"style.css\">", false},
				{"<style src=\"style.css\">", false},
				{"<include css=\"style.css\">", false},
			},
		},
		{
			Text:       "Which HTML5 input types are valid?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html5,forms,input",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"email", true},
				{"date", true},
				{"phone", false},
				{"number", true},
			},
		},
		{
			Text:       "What is the purpose of the DOCTYPE declaration?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html,doctype,standards",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To tell the browser which version of HTML the page is using", true},
				{"To include CSS styles", false},
				{"To define the page title", false},
				{"To create a comment", false},
			},
		},
		{
			Text:       "Which attributes are required for the <img> tag?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html,images,attributes",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"src", true},
				{"alt", true},
				{"width", false},
				{"height", false},
			},
		},
		{
			Text:       "What is the difference between <div> and <span>?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html,elements,display",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<div> is block-level, <span> is inline", true},
				{"<div> is inline, <span> is block-level", false},
				{"They are exactly the same", false},
				{"<div> is for text, <span> is for images", false},
			},
		},
		{
			Text:       "Which HTML5 elements provide better document structure?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html5,semantic,structure",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<article>", true},
				{"<aside>", true},
				{"<div>", false},
				{"<nav>", true},
			},
		},
		{
			Text:       "What is the correct way to create a form in HTML?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html,forms,structure",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<form action=\"submit.php\" method=\"post\">", true},
				{"<form href=\"submit.php\">", false},
				{"<input form=\"submit.php\">", false},
				{"<form src=\"submit.php\">", false},
			},
		},
		{
			Text:       "Which meta tags are important for SEO?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html,meta,seo",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"description", true},
				{"keywords", true},
				{"author", false},
				{"viewport", false},
			},
		},
		{
			Text:       "What is the purpose of the <head> section?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "html,head,metadata",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To contain metadata about the document", true},
				{"To display the main content", false},
				{"To create the page header", false},
				{"To define navigation links", false},
			},
		},

		// Hard questions (10 questions)
		{
			Text:       "Which HTML5 attributes improve accessibility?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html5,accessibility,aria",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"aria-label", true},
				{"role", true},
				{"class", false},
				{"alt", true},
			},
		},
		{
			Text:       "What does the 'defer' attribute do in script tags?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html,javascript,defer,performance",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Delays script execution until HTML parsing is complete", true},
				{"Loads script asynchronously", false},
				{"Prevents script from executing", false},
				{"Caches the script for faster loading", false},
			},
		},
		{
			Text:       "Which HTML5 APIs provide advanced functionality?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html5,apis,advanced",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Geolocation API", true},
				{"Local Storage API", true},
				{"CSS API", false},
				{"Canvas API", true},
			},
		},
		{
			Text:       "What is the difference between 'async' and 'defer' attributes?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html,javascript,async,defer",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"async executes immediately when loaded, defer waits for HTML parsing", true},
				{"defer executes immediately, async waits for HTML parsing", false},
				{"They are exactly the same", false},
				{"async is for CSS, defer is for JavaScript", false},
			},
		},
		{
			Text:       "Which Content Security Policy directives are most important?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html,security,csp",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"script-src", true},
				{"style-src", true},
				{"font-src", false},
				{"default-src", true},
			},
		},
		{
			Text:       "What is the purpose of the 'preload' link relation?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html,performance,preload",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To load critical resources early in the page lifecycle", true},
				{"To prevent resources from loading", false},
				{"To load resources after page load", false},
				{"To compress resources", false},
			},
		},
		{
			Text:       "Which HTML5 form validation attributes provide client-side validation?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html5,forms,validation",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"required", true},
				{"pattern", true},
				{"placeholder", false},
				{"min/max", true},
			},
		},
		{
			Text:       "What is the purpose of the 'crossorigin' attribute?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html,cors,security",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To control how cross-origin requests are handled", true},
				{"To prevent cross-origin requests", false},
				{"To enable all cross-origin requests", false},
				{"To compress cross-origin data", false},
			},
		},
		{
			Text:       "Which HTML5 elements support the 'contenteditable' attribute?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html5,contenteditable,editing",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"div", true},
				{"p", true},
				{"img", false},
				{"span", true},
			},
		},
		{
			Text:       "What is the difference between 'rel=preload' and 'rel=prefetch'?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "html,performance,optimization",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"preload is for current page resources, prefetch is for future navigation", true},
				{"prefetch is for current page, preload is for future navigation", false},
				{"They are exactly the same", false},
				{"preload is faster than prefetch", false},
			},
		},
	}
}

func getCSSBankQuestions() []BankQuestionData {
	return []BankQuestionData{
		// Easy questions (10 questions)
		{
			Text:       "Which CSS property is used to change text color?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,color,text",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"color", true},
				{"text-color", false},
				{"font-color", false},
				{"background-color", false},
			},
		},
		{
			Text:       "Which CSS property is used to change the background color?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,background,color",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"background-color", true},
				{"bg-color", false},
				{"color", false},
				{"background", false},
			},
		},
		{
			Text:       "How do you add a CSS file to an HTML document?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,linking,html",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"<link rel=\"stylesheet\" href=\"style.css\">", true},
				{"<css>style.css</css>", false},
				{"<style src=\"style.css\">", false},
				{"<link src=\"style.css\">", false},
			},
		},
		{
			Text:       "Which CSS property controls the text size?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,font,size",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"font-size", true},
				{"text-size", false},
				{"font-style", false},
				{"text-style", false},
			},
		},
		{
			Text:       "Which CSS properties are used for spacing?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,spacing,layout",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"margin", true},
				{"padding", true},
				{"border", false},
				{"spacing", false},
			},
		},
		{
			Text:       "What does CSS stand for?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,basics,acronym",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Cascading Style Sheets", true},
				{"Computer Style Sheets", false},
				{"Creative Style Sheets", false},
				{"Colorful Style Sheets", false},
			},
		},
		{
			Text:       "Which CSS property is used to make text bold?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,font,weight",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"font-weight", true},
				{"text-weight", false},
				{"font-style", false},
				{"text-decoration", false},
			},
		},
		{
			Text:       "Which CSS property is used to center text?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,text,alignment",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"text-align", true},
				{"align", false},
				{"text-center", false},
				{"center", false},
			},
		},
		{
			Text:       "Which CSS selector targets elements by class?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,selectors,class",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{".classname", true},
				{"#classname", false},
				{"classname", false},
				{"*classname", false},
			},
		},
		{
			Text:       "Which CSS selector targets elements by ID?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "css,selectors,id",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"#idname", true},
				{".idname", false},
				{"idname", false},
				{"*idname", false},
			},
		},

		// Normal questions (10 questions)
		{
			Text:       "Which CSS display values create block-level elements?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,display,layout",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"block", true},
				{"inline", false},
				{"flex", true},
				{"grid", true},
			},
		},
		{
			Text:       "What is the CSS box model?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,box-model,layout",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Content, padding, border, and margin", true},
				{"Width, height, and color", false},
				{"Font, size, and weight", false},
				{"Top, right, bottom, and left", false},
			},
		},
		{
			Text:       "Which CSS units are relative?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,units,responsive",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"em", true},
				{"px", false},
				{"rem", true},
				{"%", true},
			},
		},
		{
			Text:       "What is the difference between margin and padding?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,spacing,box-model",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Margin is outside the border, padding is inside", true},
				{"Padding is outside the border, margin is inside", false},
				{"They are exactly the same", false},
				{"Margin is for text, padding is for images", false},
			},
		},
		{
			Text:       "Which CSS properties are used for flexbox?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,flexbox,layout",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"justify-content", true},
				{"align-items", true},
				{"text-align", false},
				{"flex-direction", true},
			},
		},
		{
			Text:       "What is the purpose of CSS media queries?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,responsive,media-queries",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To apply different styles based on device characteristics", true},
				{"To load external CSS files", false},
				{"To validate CSS syntax", false},
				{"To compress CSS files", false},
			},
		},
		{
			Text:       "Which CSS pseudo-classes are commonly used?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,pseudo-classes,selectors",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{":hover", true},
				{":focus", true},
				{":click", false},
				{":active", true},
			},
		},
		{
			Text:       "What is the CSS cascade?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,cascade,inheritance",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"The process of combining multiple stylesheets and resolving conflicts", true},
				{"A CSS animation effect", false},
				{"A CSS layout technique", false},
				{"A CSS validation tool", false},
			},
		},
		{
			Text:       "Which CSS properties control element positioning?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,positioning,layout",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"position", true},
				{"top", true},
				{"display", false},
				{"z-index", true},
			},
		},
		{
			Text:       "What is the difference between inline and block elements?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "css,display,elements",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Block elements take full width, inline elements only take necessary width", true},
				{"Inline elements take full width, block elements only take necessary width", false},
				{"They are exactly the same", false},
				{"Block elements are for text, inline elements are for images", false},
			},
		},

		// Hard questions (10 questions)
		{
			Text:       "What is CSS specificity?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,specificity,selectors",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A system that determines which CSS rules apply when multiple rules target the same element", true},
				{"The speed at which CSS loads", false},
				{"The number of CSS files in a project", false},
				{"A CSS validation tool", false},
			},
		},
		{
			Text:       "Which CSS Grid properties define the grid structure?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,grid,layout",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"grid-template-columns", true},
				{"grid-template-rows", true},
				{"grid-gap", false},
				{"grid-template-areas", true},
			},
		},
		{
			Text:       "What is the CSS containment property used for?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,containment,performance",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To optimize rendering performance by isolating subtrees", true},
				{"To prevent CSS from loading", false},
				{"To contain text within elements", false},
				{"To create CSS animations", false},
			},
		},
		{
			Text:       "Which CSS custom properties features are advanced?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,custom-properties,variables",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Inheritance", true},
				{"Fallback values", true},
				{"Static typing", false},
				{"Runtime modification", true},
			},
		},
		{
			Text:       "What is the CSS logical properties approach?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,logical-properties,internationalization",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Using start/end instead of left/right for better internationalization", true},
				{"Using mathematical logic in CSS", false},
				{"A CSS debugging technique", false},
				{"A CSS optimization method", false},
			},
		},
		{
			Text:       "Which CSS features improve performance?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,performance,optimization",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"will-change", true},
				{"transform", true},
				{"color", false},
				{"contain", true},
			},
		},
		{
			Text:       "What is the CSS Houdini project?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,houdini,apis",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A set of APIs that expose parts of the CSS engine to developers", true},
				{"A CSS framework", false},
				{"A CSS preprocessor", false},
				{"A CSS validation tool", false},
			},
		},
		{
			Text:       "Which CSS at-rules are used for advanced features?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,at-rules,advanced",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"@supports", true},
				{"@layer", true},
				{"@color", false},
				{"@container", true},
			},
		},
		{
			Text:       "What is CSS subgrid?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,subgrid,layout",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A way for grid items to participate in their parent's grid", true},
				{"A smaller version of CSS Grid", false},
				{"A CSS animation technique", false},
				{"A CSS debugging tool", false},
			},
		},
		{
			Text:       "Which CSS cascade layers features provide better control?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "css,cascade-layers,architecture",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"@layer rule", true},
				{"Layer ordering", true},
				{"Automatic layering", false},
				{"Unlayered styles", true},
			},
		},
	}
}

func getJavaScriptBankQuestions() []BankQuestionData {
	return []BankQuestionData{
		// Easy questions (10 questions)
		{
			Text:       "How do you declare a variable in JavaScript?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,variables,declaration",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"var name", true},
				{"let name", true},
				{"const name", true},
				{"variable name", false},
			},
		},
		{
			Text:       "Which method is used to write output in JavaScript?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,output,console",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"console.log()", true},
				{"print()", false},
				{"write()", false},
				{"output()", false},
			},
		},
		{
			Text:       "What is the correct way to create a function in JavaScript?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,functions,syntax",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"function myFunction() {}", true},
				{"create myFunction() {}", false},
				{"def myFunction() {}", false},
				{"function = myFunction() {}", false},
			},
		},
		{
			Text:       "Which JavaScript data types are primitive?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,data-types,primitives",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"string", true},
				{"number", true},
				{"object", false},
				{"boolean", true},
			},
		},
		{
			Text:       "How do you create a comment in JavaScript?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,comments,syntax",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"// This is a comment", true},
				{"# This is a comment", false},
				{"<!-- This is a comment -->", false},
				{"* This is a comment *", false},
			},
		},
		{
			Text:       "Which operator is used for string concatenation?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,strings,operators",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"+", true},
				{"&", false},
				{"*", false},
				{".", false},
			},
		},
		{
			Text:       "What is the correct way to write a JavaScript array?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,arrays,syntax",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"var colors = ['red', 'green', 'blue']", true},
				{"var colors = (1:'red', 2:'green', 3:'blue')", false},
				{"var colors = 'red', 'green', 'blue'", false},
				{"var colors = 1 = 'red', 2 = 'green', 3 = 'blue'", false},
			},
		},
		{
			Text:       "Which method is used to add an element to the end of an array?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,arrays,methods",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"push()", true},
				{"add()", false},
				{"append()", false},
				{"insert()", false},
			},
		},
		{
			Text:       "What does 'null' represent in JavaScript?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,null,data-types",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"An intentional absence of any object value", true},
				{"An undefined variable", false},
				{"An empty string", false},
				{"Zero value", false},
			},
		},
		{
			Text:       "Which statement is used to stop a loop?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "javascript,loops,control",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"break", true},
				{"stop", false},
				{"exit", false},
				{"end", false},
			},
		},

		// Normal questions (10 questions)
		{
			Text:       "What is the difference between '==' and '===' in JavaScript?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,comparison,operators",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"== compares values, === compares values and types", true},
				{"They are exactly the same", false},
				{"=== is faster than ==", false},
				{"== is for numbers, === is for strings", false},
			},
		},
		{
			Text:       "Which JavaScript features support asynchronous programming?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,async,promises",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Promises", true},
				{"async/await", true},
				{"for loops", false},
				{"callbacks", true},
			},
		},
		{
			Text:       "What is the purpose of the 'this' keyword?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,this,context",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Refers to the object that is executing the current function", true},
				{"Creates a new variable", false},
				{"Defines a constant", false},
				{"Imports a module", false},
			},
		},
		{
			Text:       "Which array methods do not modify the original array?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,arrays,immutable",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"map()", true},
				{"filter()", true},
				{"push()", false},
				{"slice()", true},
			},
		},
		{
			Text:       "What is closure in JavaScript?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,closures,scope",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A function that has access to variables in its outer scope", true},
				{"A way to close a program", false},
				{"A type of loop", false},
				{"A method to hide variables", false},
			},
		},
		{
			Text:       "Which ES6 features improve code readability?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,es6,features",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Template literals", true},
				{"Arrow functions", true},
				{"var keyword", false},
				{"Destructuring", true},
			},
		},
		{
			Text:       "What is the difference between 'let' and 'var'?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,variables,scope",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"let has block scope, var has function scope", true},
				{"var has block scope, let has function scope", false},
				{"They are exactly the same", false},
				{"let is faster than var", false},
			},
		},
		{
			Text:       "Which methods are used for DOM manipulation?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,dom,manipulation",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"getElementById()", true},
				{"querySelector()", true},
				{"console.log()", false},
				{"addEventListener()", true},
			},
		},
		{
			Text:       "What is event bubbling?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,events,bubbling",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Events propagate from child to parent elements", true},
				{"Events propagate from parent to child elements", false},
				{"Events are cancelled automatically", false},
				{"Events are duplicated", false},
			},
		},
		{
			Text:       "Which JavaScript concepts relate to functional programming?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "javascript,functional,programming",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Higher-order functions", true},
				{"Pure functions", true},
				{"Global variables", false},
				{"Immutability", true},
			},
		},

		// Hard questions (10 questions)
		{
			Text:       "What is the JavaScript event loop?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,event-loop,async",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A mechanism that handles asynchronous operations in single-threaded JavaScript", true},
				{"A type of loop for iterating arrays", false},
				{"A method for creating events", false},
				{"A debugging tool", false},
			},
		},
		{
			Text:       "Which advanced JavaScript patterns improve code organization?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,patterns,architecture",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Module pattern", true},
				{"Observer pattern", true},
				{"Global variables", false},
				{"Prototype pattern", true},
			},
		},
		{
			Text:       "What is the difference between call, apply, and bind?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,context,methods",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"call/apply invoke immediately, bind returns a new function", true},
				{"They are exactly the same", false},
				{"bind is faster than call and apply", false},
				{"call is for objects, apply is for arrays", false},
			},
		},
		{
			Text:       "Which JavaScript memory management concepts are important?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,memory,garbage-collection",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Garbage collection", true},
				{"Memory leaks", true},
				{"Automatic allocation", false},
				{"Reference counting", true},
			},
		},
		{
			Text:       "What is the purpose of JavaScript Proxies?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,proxies,metaprogramming",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To intercept and customize operations on objects", true},
				{"To create network connections", false},
				{"To handle errors", false},
				{"To optimize performance", false},
			},
		},
		{
			Text:       "Which JavaScript engine optimizations affect performance?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,performance,optimization",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"JIT compilation", true},
				{"Hidden classes", true},
				{"Global variables", false},
				{"Inline caching", true},
			},
		},
		{
			Text:       "What is the difference between microtasks and macrotasks?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,event-loop,tasks",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Microtasks have higher priority and execute before macrotasks", true},
				{"Macrotasks have higher priority than microtasks", false},
				{"They have the same priority", false},
				{"They are executed in random order", false},
			},
		},
		{
			Text:       "Which JavaScript features support metaprogramming?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,metaprogramming,reflection",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Reflect API", true},
				{"Proxy objects", true},
				{"console.log", false},
				{"Symbol.iterator", true},
			},
		},
		{
			Text:       "What is the purpose of WeakMap and WeakSet?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,weakmap,memory",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To hold weak references that don't prevent garbage collection", true},
				{"To create faster data structures", false},
				{"To store large amounts of data", false},
				{"To create immutable collections", false},
			},
		},
		{
			Text:       "Which JavaScript concurrency patterns are advanced?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "javascript,concurrency,async",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Web Workers", true},
				{"SharedArrayBuffer", true},
				{"setTimeout", false},
				{"Atomics", true},
			},
		},
	}
}

func getProgrammingLogicBankQuestions() []BankQuestionData {
	return []BankQuestionData{
		// Easy questions (10 questions)
		{
			Text:       "What is a loop in programming?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,loops,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A structure that repeats a block of code", true},
				{"A type of variable", false},
				{"A function", false},
				{"A data type", false},
			},
		},
		{
			Text:       "What is a variable in programming?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,variables,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A container for storing data values", true},
				{"A type of loop", false},
				{"A function", false},
				{"A programming language", false},
			},
		},
		{
			Text:       "Which are basic programming constructs?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,constructs,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Sequence", true},
				{"Selection", true},
				{"Compilation", false},
				{"Iteration", true},
			},
		},
		{
			Text:       "What is an algorithm?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,algorithms,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A step-by-step procedure to solve a problem", true},
				{"A programming language", false},
				{"A type of variable", false},
				{"A computer program", false},
			},
		},
		{
			Text:       "What is a function in programming?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,functions,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A reusable block of code that performs a specific task", true},
				{"A type of variable", false},
				{"A loop structure", false},
				{"A data type", false},
			},
		},
		{
			Text:       "Which are common data types in programming?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,data-types,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Integer", true},
				{"String", true},
				{"Loop", false},
				{"Boolean", true},
			},
		},
		{
			Text:       "What is debugging?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,debugging,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"The process of finding and fixing errors in code", true},
				{"Writing new code", false},
				{"Compiling code", false},
				{"Running code", false},
			},
		},
		{
			Text:       "What is pseudocode?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,pseudocode,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"An informal high-level description of a program's logic", true},
				{"A programming language", false},
				{"A type of error", false},
				{"A debugging tool", false},
			},
		},
		{
			Text:       "Which are types of programming paradigms?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,paradigms,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Procedural", true},
				{"Object-oriented", true},
				{"Debugging", false},
				{"Functional", true},
			},
		},
		{
			Text:       "What is an array?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "programming,arrays,data-structures",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A collection of elements stored in contiguous memory locations", true},
				{"A type of loop", false},
				{"A function", false},
				{"A variable", false},
			},
		},

		// Normal questions (10 questions)
		{
			Text:       "What is the time complexity of binary search?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "algorithms,complexity,search",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"O(log n)", true},
				{"O(n)", false},
				{"O(n²)", false},
				{"O(1)", false},
			},
		},
		{
			Text:       "Which data structures use LIFO principle?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "data-structures,stack,lifo",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Stack", true},
				{"Queue", false},
				{"Call Stack", true},
				{"Array", false},
			},
		},
		{
			Text:       "What is recursion?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "programming,recursion,algorithms",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A function that calls itself", true},
				{"A type of loop", false},
				{"A data structure", false},
				{"A sorting algorithm", false},
			},
		},
		{
			Text:       "Which sorting algorithms have O(n log n) average time complexity?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "algorithms,sorting,complexity",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Merge Sort", true},
				{"Bubble Sort", false},
				{"Quick Sort", true},
				{"Heap Sort", true},
			},
		},
		{
			Text:       "What is the difference between breadth-first and depth-first search?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "algorithms,graph,search",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"BFS explores level by level, DFS explores as deep as possible first", true},
				{"DFS explores level by level, BFS explores as deep as possible first", false},
				{"They are exactly the same", false},
				{"BFS is for trees, DFS is for graphs", false},
			},
		},
		{
			Text:       "Which are characteristics of object-oriented programming?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "programming,oop,concepts",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Encapsulation", true},
				{"Inheritance", true},
				{"Compilation", false},
				{"Polymorphism", true},
			},
		},
		{
			Text:       "What is dynamic programming?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "algorithms,dynamic-programming,optimization",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A method for solving problems by breaking them into overlapping subproblems", true},
				{"A programming language feature", false},
				{"A type of memory allocation", false},
				{"A debugging technique", false},
			},
		},
		{
			Text:       "Which are common tree traversal methods?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "data-structures,trees,traversal",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Inorder", true},
				{"Preorder", true},
				{"Random", false},
				{"Postorder", true},
			},
		},
		{
			Text:       "What is the purpose of hash tables?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "data-structures,hash-tables,performance",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To provide fast insertion, deletion, and lookup operations", true},
				{"To sort data automatically", false},
				{"To store data in order", false},
				{"To compress data", false},
			},
		},
		{
			Text:       "Which are principles of good software design?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "programming,design,principles",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"DRY (Don't Repeat Yourself)", true},
				{"SOLID principles", true},
				{"Complex coupling", false},
				{"Single Responsibility", true},
			},
		},

		// Hard questions (10 questions)
		{
			Text:       "What is the space complexity of merge sort?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "algorithms,sorting,complexity",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"O(n)", true},
				{"O(log n)", false},
				{"O(1)", false},
				{"O(n log n)", false},
			},
		},
		{
			Text:       "Which advanced algorithmic techniques optimize performance?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "algorithms,optimization,advanced",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Memoization", true},
				{"Divide and conquer", true},
				{"Brute force", false},
				{"Greedy algorithms", true},
			},
		},
		{
			Text:       "What is the difference between P and NP complexity classes?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "algorithms,complexity,theory",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"P problems can be solved in polynomial time, NP problems can be verified in polynomial time", true},
				{"P is faster than NP", false},
				{"NP problems cannot be solved", false},
				{"P and NP are the same", false},
			},
		},
		{
			Text:       "Which design patterns solve common software problems?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "programming,design-patterns,architecture",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Singleton", true},
				{"Observer", true},
				{"Global variable", false},
				{"Factory", true},
			},
		},
		{
			Text:       "What is the purpose of graph algorithms like Dijkstra's?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "algorithms,graphs,shortest-path",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To find the shortest path between nodes in a weighted graph", true},
				{"To sort graph nodes", false},
				{"To detect cycles in graphs", false},
				{"To compress graph data", false},
			},
		},
		{
			Text:       "Which are characteristics of functional programming?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "programming,functional,paradigms",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Immutability", true},
				{"Pure functions", true},
				{"Global state", false},
				{"Higher-order functions", true},
			},
		},
		{
			Text:       "What is the purpose of concurrent programming?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "programming,concurrency,parallelism",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To execute multiple tasks simultaneously or in overlapping time periods", true},
				{"To make programs run faster", false},
				{"To use less memory", false},
				{"To simplify code", false},
			},
		},
		{
			Text:       "Which are advanced data structures for specific use cases?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "data-structures,advanced,specialized",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Trie", true},
				{"B-tree", true},
				{"Array", false},
				{"Bloom filter", true},
			},
		},
		{
			Text:       "What is the purpose of compiler optimization?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "programming,compilers,optimization",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To improve program performance by transforming code", true},
				{"To make code easier to read", false},
				{"To reduce file size", false},
				{"To add debugging information", false},
			},
		},
		{
			Text:       "Which are principles of distributed systems design?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "programming,distributed,systems",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"CAP theorem", true},
				{"Eventual consistency", true},
				{"Single point of failure", false},
				{"Fault tolerance", true},
			},
		},
	}
}

func getDatabaseBankQuestions() []BankQuestionData {
	return []BankQuestionData{
		// Easy questions (10 questions)
		{
			Text:       "What does SQL stand for?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "sql,database,acronym",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Structured Query Language", true},
				{"Simple Query Language", false},
				{"Standard Query Language", false},
				{"System Query Language", false},
			},
		},
		{
			Text:       "Which SQL command is used to retrieve data?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "sql,select,query",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"SELECT", true},
				{"GET", false},
				{"RETRIEVE", false},
				{"FETCH", false},
			},
		},
		{
			Text:       "What is a primary key?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "database,primary-key,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A unique identifier for each record in a table", true},
				{"A password for database access", false},
				{"The first column in a table", false},
				{"A backup key", false},
			},
		},
		{
			Text:       "Which SQL commands are used for data manipulation?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "sql,dml,commands",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"INSERT", true},
				{"UPDATE", true},
				{"CREATE", false},
				{"DELETE", true},
			},
		},
		{
			Text:       "What is a database table?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "database,table,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A collection of related data organized in rows and columns", true},
				{"A type of database software", false},
				{"A database backup", false},
				{"A user account", false},
			},
		},
		{
			Text:       "Which SQL clause is used to filter results?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "sql,where,filtering",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"WHERE", true},
				{"FILTER", false},
				{"HAVING", false},
				{"LIMIT", false},
			},
		},
		{
			Text:       "What does CRUD stand for in database operations?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "database,crud,operations",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Create, Read, Update, Delete", true},
				{"Connect, Retrieve, Update, Drop", false},
				{"Copy, Read, Upload, Download", false},
				{"Create, Restore, Update, Destroy", false},
			},
		},
		{
			Text:       "Which data types are commonly used in databases?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "database,data-types,basics",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"VARCHAR", true},
				{"INTEGER", true},
				{"LOOP", false},
				{"DATE", true},
			},
		},
		{
			Text:       "What is a database schema?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "database,schema,structure",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"The structure and organization of a database", true},
				{"A database backup file", false},
				{"A type of query", false},
				{"A user permission", false},
			},
		},
		{
			Text:       "Which SQL command is used to create a new table?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     1,
			Difficulty: models.DifficultyEasy,
			Tags:       "sql,ddl,create",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"CREATE TABLE", true},
				{"NEW TABLE", false},
				{"MAKE TABLE", false},
				{"ADD TABLE", false},
			},
		},

		// Normal questions (10 questions)
		{
			Text:       "What is a foreign key?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "database,keys,relationships",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A field that refers to the primary key of another table", true},
				{"A unique identifier for a table", false},
				{"An index on a table", false},
				{"A constraint that prevents null values", false},
			},
		},
		{
			Text:       "Which normal forms eliminate partial dependencies?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "database,normalization,normal-forms",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"2NF", true},
				{"1NF", false},
				{"3NF", true},
				{"BCNF", true},
			},
		},
		{
			Text:       "What is the purpose of database indexing?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "database,indexing,performance",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To improve query performance by creating faster access paths", true},
				{"To backup database data", false},
				{"To encrypt sensitive data", false},
				{"To compress database files", false},
			},
		},
		{
			Text:       "Which SQL JOIN types are commonly used?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "sql,joins,relationships",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"INNER JOIN", true},
				{"LEFT JOIN", true},
				{"MIDDLE JOIN", false},
				{"RIGHT JOIN", true},
			},
		},
		{
			Text:       "What is database normalization?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "database,normalization,design",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"The process of organizing data to reduce redundancy and improve integrity", true},
				{"Making all data the same format", false},
				{"Backing up the database", false},
				{"Encrypting database data", false},
			},
		},
		{
			Text:       "Which SQL aggregate functions are commonly used?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "sql,aggregate,functions",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"COUNT", true},
				{"SUM", true},
				{"CONCAT", false},
				{"AVG", true},
			},
		},
		{
			Text:       "What is the difference between DELETE and TRUNCATE?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "sql,delete,truncate",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"DELETE can use WHERE clause, TRUNCATE removes all rows", true},
				{"TRUNCATE can use WHERE clause, DELETE removes all rows", false},
				{"They are exactly the same", false},
				{"DELETE is faster than TRUNCATE", false},
			},
		},
		{
			Text:       "Which database constraints ensure data integrity?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "database,constraints,integrity",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"NOT NULL", true},
				{"UNIQUE", true},
				{"INDEX", false},
				{"CHECK", true},
			},
		},
		{
			Text:       "What is a database view?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "database,views,virtual-tables",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"A virtual table based on the result of a SQL query", true},
				{"A way to look at database files", false},
				{"A backup of database data", false},
				{"A user interface for databases", false},
			},
		},
		{
			Text:       "Which are characteristics of ACID properties?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     2,
			Difficulty: models.DifficultyNormal,
			Tags:       "database,acid,transactions",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Atomicity", true},
				{"Consistency", true},
				{"Availability", false},
				{"Durability", true},
			},
		},

		// Hard questions (10 questions)
		{
			Text:       "Which isolation levels prevent dirty reads?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,isolation,transactions",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"READ COMMITTED", true},
				{"READ UNCOMMITTED", false},
				{"REPEATABLE READ", true},
				{"SERIALIZABLE", true},
			},
		},
		{
			Text:       "What is the purpose of database sharding?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,sharding,scalability",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Horizontal partitioning to distribute data across multiple servers", true},
				{"Creating backup copies of data", false},
				{"Encrypting sensitive data", false},
				{"Compressing data to save space", false},
			},
		},
		{
			Text:       "Which advanced indexing techniques improve performance?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,indexing,advanced",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"B-tree indexes", true},
				{"Hash indexes", true},
				{"Linear indexes", false},
				{"Bitmap indexes", true},
			},
		},
		{
			Text:       "What is the CAP theorem in distributed databases?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,cap-theorem,distributed",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"You can only guarantee 2 of 3: Consistency, Availability, Partition tolerance", true},
				{"All three properties can always be guaranteed", false},
				{"Only one property can be guaranteed at a time", false},
				{"CAP stands for Create, Alter, Partition", false},
			},
		},
		{
			Text:       "Which database replication strategies are commonly used?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,replication,high-availability",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Master-slave replication", true},
				{"Master-master replication", true},
				{"Single-point replication", false},
				{"Asynchronous replication", true},
			},
		},
		{
			Text:       "What is the purpose of database connection pooling?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,connection-pooling,performance",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To reuse database connections and improve performance", true},
				{"To backup database connections", false},
				{"To encrypt database connections", false},
				{"To monitor database usage", false},
			},
		},
		{
			Text:       "Which NoSQL database types serve different use cases?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,nosql,types",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Document stores", true},
				{"Key-value stores", true},
				{"Relational stores", false},
				{"Graph databases", true},
			},
		},
		{
			Text:       "What is eventual consistency in distributed systems?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,consistency,distributed",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"The system will become consistent over time if no new updates are made", true},
				{"The system is always consistent", false},
				{"The system is never consistent", false},
				{"Consistency is checked every hour", false},
			},
		},
		{
			Text:       "Which database optimization techniques improve query performance?",
			Type:       models.QuestionTypeMultipleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,optimization,performance",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"Query plan optimization", true},
				{"Materialized views", true},
				{"Random access", false},
				{"Partitioning", true},
			},
		},
		{
			Text:       "What is the purpose of database triggers?",
			Type:       models.QuestionTypeSingleChoice,
			Points:     3,
			Difficulty: models.DifficultyHard,
			Tags:       "database,triggers,automation",
			Options: []struct {
				Text      string
				IsCorrect bool
			}{
				{"To automatically execute code in response to database events", true},
				{"To manually start database operations", false},
				{"To schedule database backups", false},
				{"To create database connections", false},
			},
		},
	}
}
