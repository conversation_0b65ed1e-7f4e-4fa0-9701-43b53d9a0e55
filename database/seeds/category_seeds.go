package seeds

import (
	"etuto-database/models"

	"gorm.io/gorm"
)

func seedCategories(db *gorm.DB) ([]models.Category, error) {
	var categories []models.Category

	categoryData := []struct {
		Name        string
		Description string
	}{
		{
			Name:        "Programming",
			Description: "Programming languages, algorithms, and software development",
		},
		{
			Name:        "Web Development",
			Description: "Frontend, backend, and full-stack web development",
		},
		{
			Name:        "Data Science",
			Description: "Data analysis, machine learning, and statistics",
		},
		{
			Name:        "Database",
			Description: "Database design, SQL, and data management",
		},
		{
			Name:        "Mobile Development",
			Description: "iOS, Android, and cross-platform mobile app development",
		},
		{
			Name:        "DevOps",
			Description: "Deployment, CI/CD, cloud computing, and infrastructure",
		},
		{
			Name:        "Cybersecurity",
			Description: "Information security, ethical hacking, and network security",
		},
		{
			Name:        "UI/UX Design",
			Description: "User interface design, user experience, and design principles",
		},
	}

	for _, catData := range categoryData {
		var category models.Category
		if err := db.Where("name = ?", catData.Name).First(&category).Error; err != nil {
			category = models.Category{
				Name:        catData.Name,
				Description: catData.Description,
			}
			if err := db.Create(&category).Error; err != nil {
				return categories, err
			}
		}
		categories = append(categories, category)
	}

	return categories, nil
}
