package seeds

import (
	"etuto-database/models"
	"etuto-database/utils"

	"gorm.io/gorm"
)

func seedUsers(db *gorm.DB) (teacher models.User, student models.User, err error) {
	// Check if teacher user exists, if not create it
	if err := db.Where("email = ?", "<EMAIL>").First(&teacher).Error; err != nil {
		teacherPassword, _ := utils.HashPassword("teacher123")
		teacher = models.User{
			Email:     "<EMAIL>",
			Password:  teacherPassword,
			FirstName: "John",
			LastName:  "Teacher",
			Role:      models.RoleTeacher,
			IsActive:  true,
		}
		if err := db.Create(&teacher).Error; err != nil {
			return teacher, student, err
		}
	}

	// Check if student user exists, if not create it
	if err := db.Where("email = ?", "<EMAIL>").First(&student).Error; err != nil {
		studentPassword, _ := utils.HashPassword("student123")
		student = models.User{
			Email:     "<EMAIL>",
			Password:  studentPassword,
			FirstName: "Jane",
			LastName:  "Student",
			Role:      models.RoleStudent,
			IsActive:  true,
		}
		if err := db.Create(&student).Error; err != nil {
			return teacher, student, err
		}
	}

	return teacher, student, nil
}
