package seeds

import (
	"etuto-database/models"

	"gorm.io/gorm"
)

func seedLessonsAndSlides(db *gorm.DB, courses []models.Course) error {
	if len(courses) == 0 {
		return nil
	}

	course1 := courses[0] // Introduction to Web Development
	course2 := courses[1] // React Fundamentals

	// Create lessons for course 1 (check if they exist first)
	var lesson1 models.Lesson
	if err := db.Where("course_id = ? AND title = ?", course1.ID, "HTML Basics").First(&lesson1).Error; err != nil {
		lesson1 = models.Lesson{
			CourseID:    course1.ID,
			Title:       "HTML Basics",
			Description: "Learn the fundamentals of HTML markup language",
			OrderIndex:  1,
		}
		if err := db.Create(&lesson1).Error; err != nil {
			return err
		}
	}

	var lesson2 models.Lesson
	if err := db.Where("course_id = ? AND title = ?", course1.ID, "CSS Styling").First(&lesson2).Error; err != nil {
		lesson2 = models.Lesson{
			CourseID:    course1.ID,
			Title:       "CSS Styling",
			Description: "Style your web pages with CSS",
			OrderIndex:  2,
		}
		if err := db.Create(&lesson2).Error; err != nil {
			return err
		}
	}

	var lesson3 models.Lesson
	if err := db.Where("course_id = ? AND title = ?", course1.ID, "JavaScript Basics").First(&lesson3).Error; err != nil {
		lesson3 = models.Lesson{
			CourseID:    course1.ID,
			Title:       "JavaScript Basics",
			Description: "Add interactivity with JavaScript",
			OrderIndex:  3,
		}
		if err := db.Create(&lesson3).Error; err != nil {
			return err
		}
	}

	// Create slides for lesson 1
	slides := []models.Slide{
		{
			LessonID:   lesson1.ID,
			Title:      "What is HTML?",
			Content:    "HTML (HyperText Markup Language) is the standard markup language for creating web pages. It describes the structure of a web page using elements and tags.",
			OrderIndex: 1,
		},
		{
			LessonID:   lesson1.ID,
			Title:      "HTML Document Structure",
			Content:    "Every HTML document has a basic structure:\n\n```html\n<!DOCTYPE html>\n<html>\n<head>\n  <title>Page Title</title>\n</head>\n<body>\n  <h1>My First Heading</h1>\n  <p>My first paragraph.</p>\n</body>\n</html>\n```",
			OrderIndex: 2,
		},
		{
			LessonID:   lesson1.ID,
			Title:      "Common HTML Elements",
			Content:    "Here are some common HTML elements:\n\n- `<h1>` to `<h6>`: Headings\n- `<p>`: Paragraphs\n- `<a>`: Links\n- `<img>`: Images\n- `<div>`: Divisions\n- `<span>`: Inline elements",
			OrderIndex: 3,
		},
	}

	for _, slide := range slides {
		var existingSlide models.Slide
		if err := db.Where("lesson_id = ? AND title = ?", slide.LessonID, slide.Title).First(&existingSlide).Error; err != nil {
			if err := db.Create(&slide).Error; err != nil {
				return err
			}
		}
	}

	// Create slides for lesson 2
	cssSlides := []models.Slide{
		{
			LessonID:   lesson2.ID,
			Title:      "What is CSS?",
			Content:    "CSS (Cascading Style Sheets) is used to style and layout web pages. It controls the visual presentation of HTML elements.",
			OrderIndex: 1,
		},
		{
			LessonID:   lesson2.ID,
			Title:      "CSS Selectors",
			Content:    "CSS selectors are used to target HTML elements:\n\n- Element selector: `p { color: blue; }`\n- Class selector: `.my-class { font-size: 16px; }`\n- ID selector: `#my-id { background: red; }`",
			OrderIndex: 2,
		},
	}

	for _, slide := range cssSlides {
		var existingSlide models.Slide
		if err := db.Where("lesson_id = ? AND title = ?", slide.LessonID, slide.Title).First(&existingSlide).Error; err != nil {
			if err := db.Create(&slide).Error; err != nil {
				return err
			}
		}
	}

	// Create lessons for course 2 (React)
	var reactLesson1 models.Lesson
	if err := db.Where("course_id = ? AND title = ?", course2.ID, "Introduction to React").First(&reactLesson1).Error; err != nil {
		reactLesson1 = models.Lesson{
			CourseID:    course2.ID,
			Title:       "Introduction to React",
			Description: "Learn what React is and why it's popular",
			OrderIndex:  1,
		}
		if err := db.Create(&reactLesson1).Error; err != nil {
			return err
		}
	}

	var reactLesson2 models.Lesson
	if err := db.Where("course_id = ? AND title = ?", course2.ID, "Components and JSX").First(&reactLesson2).Error; err != nil {
		reactLesson2 = models.Lesson{
			CourseID:    course2.ID,
			Title:       "Components and JSX",
			Description: "Understanding React components and JSX syntax",
			OrderIndex:  2,
		}
		if err := db.Create(&reactLesson2).Error; err != nil {
			return err
		}
	}

	// Create slides for React lesson 1
	reactSlides := []models.Slide{
		{
			LessonID:   reactLesson1.ID,
			Title:      "What is React?",
			Content:    "React is a JavaScript library for building user interfaces. It was created by Facebook and is now maintained by Meta and the open-source community.",
			OrderIndex: 1,
		},
		{
			LessonID:   reactLesson1.ID,
			Title:      "Why Use React?",
			Content:    "React offers several benefits:\n\n- **Component-based**: Build encapsulated components that manage their own state\n- **Virtual DOM**: Efficient updates and rendering\n- **Declarative**: Describe what the UI should look like\n- **Learn Once, Write Anywhere**: Use React for web, mobile, and desktop apps",
			OrderIndex: 2,
		},
	}

	for _, slide := range reactSlides {
		var existingSlide models.Slide
		if err := db.Where("lesson_id = ? AND title = ?", slide.LessonID, slide.Title).First(&existingSlide).Error; err != nil {
			if err := db.Create(&slide).Error; err != nil {
				return err
			}
		}
	}

	return nil
}
