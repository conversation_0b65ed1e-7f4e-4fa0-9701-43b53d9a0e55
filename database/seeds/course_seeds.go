package seeds

import (
	"etuto-database/models"

	"gorm.io/gorm"
)

func seedCourses(db *gorm.DB, teacher models.User) ([]models.Course, error) {
	var courses []models.Course

	// Create sample courses (check if they exist first)
	var course1 models.Course
	if err := db.Where("title = ?", "Introduction to Web Development").First(&course1).Error; err != nil {
		course1 = models.Course{
			Title:       "Introduction to Web Development",
			Description: "Learn the basics of HTML, CSS, and JavaScript to build modern web applications.",
			CreatorID:   teacher.ID,
			IsPublished: true,
		}
		if err := db.Create(&course1).Error; err != nil {
			return courses, err
		}
	}
	courses = append(courses, course1)

	var course2 models.Course
	if err := db.Where("title = ?", "React Fundamentals").First(&course2).Error; err != nil {
		course2 = models.Course{
			Title:       "React Fundamentals",
			Description: "Master React.js and build interactive user interfaces with components, hooks, and state management.",
			CreatorID:   teacher.ID,
			IsPublished: true,
		}
		if err := db.Create(&course2).Error; err != nil {
			return courses, err
		}
	}
	courses = append(courses, course2)

	var course3 models.Course
	if err := db.Where("title = ?", "Database Design").First(&course3).Error; err != nil {
		course3 = models.Course{
			Title:       "Database Design",
			Description: "Learn how to design efficient databases with PostgreSQL and understand relational database concepts.",
			CreatorID:   teacher.ID,
			IsPublished: false, // Draft course
		}
		if err := db.Create(&course3).Error; err != nil {
			return courses, err
		}
	}
	courses = append(courses, course3)

	return courses, nil
}
