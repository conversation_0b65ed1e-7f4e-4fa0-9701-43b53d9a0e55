# Hướng dẫn sử dụng Slide Editor

## Tổng quan

Tính năng Slide Editor mới cho phép tạo và chỉnh sửa slide bằng Markdown với preview theo thời gian thực. Đây là một công cụ mạnh mẽ để tạo nội dung giáo dục với giao diện trực quan và dễ sử dụng.

## Tính năng chính

### ✨ Editor với Live Preview
- **Split View**: Hiển thị editor và preview cùng lúc
- **Edit Only**: Chỉ hiển thị editor để tập trung viết
- **Preview Only**: Chỉ hiển thị preview để xem kết quả cuối cùng
- **Real-time Update**: Preview cập nhật ngay khi bạn gõ

### 📝 Hỗ trợ Markdown đầy đủ
- Headers (H1, H2, H3, ...)
- **Bold** và *italic* text
- Lists và numbered lists
- Code blocks và inline `code`
- Links và images
- Tables
- Blockquotes
- Task lists

### 🎯 Tính năng nâng cao
- Syntax highlighting cho code blocks
- GitHub Flavored Markdown (GFM)
- Hỗ trợ HTML raw
- Responsive design
- Keyboard shortcuts

## Cách sử dụng

### 1. Truy cập Demo
1. Đăng nhập vào hệ thống
2. Vào Dashboard
3. Click vào card "Try the New Slide Editor"
4. Hoặc truy cập trực tiếp: `http://localhost:5174/slide-editor-demo`

### 2. Tạo slide mới cho lesson
1. Vào trang quản lý course
2. Chọn lesson cần tạo slide
3. Click "Manage Slides" hoặc "Create Slide"
4. Sử dụng editor để tạo nội dung

### 3. Chỉnh sửa slide hiện có
1. Vào trang quản lý slides của lesson
2. Click "Edit" trên slide cần chỉnh sửa
3. Sử dụng editor để cập nhật nội dung

## Cấu trúc Routes

```
/slide-editor-demo              # Demo page
/lessons/:lessonId/slides       # Quản lý slides của lesson
/lessons/:lessonId/slides/create # Tạo slide mới
/slides/:slideId/edit           # Chỉnh sửa slide
```

## Components

### SlideEditor
Component chính cung cấp giao diện editor với các tính năng:
- Markdown editor với syntax highlighting
- Live preview với GitHub Markdown CSS
- Toggle giữa các chế độ view
- Save/Cancel functionality

### SlideCreate
Trang tạo slide mới với:
- Form nhập title và image URL
- Tích hợp SlideEditor
- Breadcrumb navigation
- Auto-generate order_index

### SlideEdit
Trang chỉnh sửa slide với:
- Load dữ liệu slide hiện có
- Tích hợp SlideEditor
- Update functionality

### SlideManagement
Trang quản lý danh sách slides với:
- Hiển thị danh sách slides
- Actions: Create, Edit, Delete, View
- Sorting theo order_index

## API Endpoints

```typescript
// Tạo slide mới
POST /slides
{
  lesson_id: number,
  title: string,
  content: string,
  image_url?: string,
  order_index: number
}

// Cập nhật slide
PUT /slides/:id
{
  title?: string,
  content?: string,
  image_url?: string,
  order_index?: number
}

// Lấy slides của lesson
GET /slides/lesson/:lessonId

// Lấy slide theo ID
GET /slides/:id

// Xóa slide
DELETE /slides/:id
```

## Markdown Examples

### Headers
```markdown
# Header 1
## Header 2
### Header 3
```

### Text Formatting
```markdown
**Bold text**
*Italic text*
`Inline code`
```

### Lists
```markdown
- Item 1
- Item 2
  - Sub item

1. Numbered item 1
2. Numbered item 2
```

### Code Blocks
```markdown
```javascript
function hello() {
  console.log('Hello, World!');
}
```
```

### Tables
```markdown
| Column 1 | Column 2 |
|----------|----------|
| Data 1   | Data 2   |
```

### Images
```markdown
![Alt text](https://example.com/image.jpg)
```

### Links
```markdown
[Link text](https://example.com)
```

### Blockquotes
```markdown
> This is a blockquote
```

## Styling

Editor sử dụng:
- GitHub Markdown CSS cho preview
- Monaco font cho editor
- Bootstrap components cho UI
- Custom CSS cho responsive design

## Keyboard Shortcuts

- `Ctrl/Cmd + S`: Save slide (nếu có handler)
- Tab trong editor: Indent
- Shift + Tab: Outdent

## Browser Support

- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support
- Mobile browsers: Responsive design

## Troubleshooting

### Preview không hiển thị
- Kiểm tra console để xem lỗi JavaScript
- Đảm bảo markdown syntax đúng

### Editor không responsive
- Kiểm tra CSS imports
- Đảm bảo Bootstrap CSS được load

### Save không hoạt động
- Kiểm tra network tab để xem API calls
- Đảm bảo backend đang chạy

## Phát triển tiếp

### Tính năng có thể thêm
- [ ] Auto-save
- [ ] Markdown toolbar
- [ ] Image upload
- [ ] Template slides
- [ ] Export to PDF
- [ ] Collaboration features
- [ ] Version history

### Cải thiện hiệu suất
- [ ] Lazy loading cho preview
- [ ] Debounce cho real-time update
- [ ] Virtual scrolling cho danh sách slides lớn

## Kết luận

Slide Editor cung cấp một trải nghiệm tạo nội dung mượt mà và trực quan. Với hỗ trợ Markdown đầy đủ và live preview, việc tạo slides giáo dục trở nên dễ dàng và hiệu quả hơn.
