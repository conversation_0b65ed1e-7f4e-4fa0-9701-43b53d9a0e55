package handlers

import (
	"net/http"
	"strconv"

	"etuto-backend/database"

	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

type CreateCourseRequest struct {
	Title       string `json:"title" binding:"required"`
	Description string `json:"description"`
	Thumbnail   string `json:"thumbnail"`
}

type UpdateCourseRequest struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Thumbnail   string `json:"thumbnail"`
	IsPublished *bool  `json:"is_published"`
}

func GetCourses(c *gin.Context) {
	var courses []models.Course
	query := database.DB.Preload("Creator").Preload("Lessons")

	// Filter by published status for students
	userRole, _ := c.Get("user_role")
	if userRole == models.RoleStudent {
		query = query.Where("is_published = ?", true)
	}

	if err := query.Find(&courses).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch courses"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"courses": courses})
}

func GetCourse(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var course models.Course
	query := database.DB.Preload("Creator").
		Preload("Lessons.Slides").
		Preload("Quizzes")

	if err := query.First(&course, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	// Check if user can access this course
	userRole, _ := c.Get("user_role")
	if userRole == models.RoleStudent && !course.IsPublished {
		c.JSON(http.StatusForbidden, gin.H{"error": "Course not available"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"course": course})
}

func CreateCourse(c *gin.Context) {
	var req CreateCourseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, _ := c.Get("user_id")
	course := models.Course{
		Title:       req.Title,
		Description: req.Description,
		Thumbnail:   req.Thumbnail,
		CreatorID:   userID.(uint),
		IsPublished: false,
	}

	if err := database.DB.Create(&course).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create course"})
		return
	}

	// Load creator information
	database.DB.Preload("Creator").First(&course, course.ID)

	c.JSON(http.StatusCreated, gin.H{"course": course})
}

func UpdateCourse(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var course models.Course
	if err := database.DB.First(&course, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	// Check if user can edit this course
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req UpdateCourseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields
	if req.Title != "" {
		course.Title = req.Title
	}
	if req.Description != "" {
		course.Description = req.Description
	}
	if req.Thumbnail != "" {
		course.Thumbnail = req.Thumbnail
	}
	if req.IsPublished != nil {
		course.IsPublished = *req.IsPublished
	}

	if err := database.DB.Save(&course).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update course"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"course": course})
}

func DeleteCourse(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var course models.Course
	if err := database.DB.First(&course, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	// Check if user can delete this course
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := database.DB.Delete(&course).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete course"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Course deleted successfully"})
}
