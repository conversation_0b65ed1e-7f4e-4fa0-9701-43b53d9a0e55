package handlers

import (
	"net/http"
	"strconv"
	"time"

	"etuto-backend/database"

	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

type CreateQuizRequest struct {
	CourseID       uint    `json:"course_id" binding:"required"`
	Title          string  `json:"title" binding:"required"`
	Description    string  `json:"description"`
	TimeLimit      int     `json:"time_limit"`
	PassScore      float64 `json:"pass_score"`
	TotalQuestions int     `json:"total_questions"`
}

type UpdateQuizRequest struct {
	Title          string   `json:"title"`
	Description    string   `json:"description"`
	TimeLimit      *int     `json:"time_limit"`
	PassScore      *float64 `json:"pass_score"`
	IsPublished    *bool    `json:"is_published"`
	TotalQuestions *int     `json:"total_questions"`
}

type SubmitQuizRequest struct {
	Answers []struct {
		QuestionID uint   `json:"question_id" binding:"required"`
		OptionIDs  []uint `json:"option_ids" binding:"required"`
	} `json:"answers" binding:"required"`
}

func GetQuizzesByCourse(c *gin.Context) {
	courseID, err := strconv.ParseUint(c.Param("course_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var quizzes []models.Quiz
	query := database.DB.Where("course_id = ?", courseID).
		Preload("Questions").
		Preload("Category")

	// Filter by published status for students
	userRole, _ := c.Get("user_role")
	if userRole == models.RoleStudent {
		query = query.Where("is_published = ?", true)
	}

	if err := query.Find(&quizzes).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quizzes"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"quizzes": quizzes})
}

func GetQuiz(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var quiz models.Quiz
	query := database.DB.Preload("Course").
		Preload("Questions.Options").
		Preload("Category")

	if err := query.First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check if user can access this quiz
	userRole, _ := c.Get("user_role")
	if userRole == models.RoleStudent && !quiz.IsPublished {
		c.JSON(http.StatusForbidden, gin.H{"error": "Quiz not available"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"quiz": quiz})
}

func CreateQuiz(c *gin.Context) {
	var req CreateQuizRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if course exists and user has permission
	var course models.Course
	if err := database.DB.First(&course, req.CourseID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	// Set default total questions if not provided
	totalQuestions := req.TotalQuestions
	if totalQuestions <= 0 {
		totalQuestions = 10 // Default value
	}

	quiz := models.Quiz{
		CourseID:       req.CourseID,
		Title:          req.Title,
		Description:    req.Description,
		TimeLimit:      req.TimeLimit,
		PassScore:      req.PassScore,
		TotalQuestions: totalQuestions,
		IsPublished:    false,
	}

	if err := database.DB.Create(&quiz).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"quiz": quiz})
}

func UpdateQuiz(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var quiz models.Quiz
	if err := database.DB.Preload("Course").First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && quiz.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req UpdateQuizRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields
	if req.Title != "" {
		quiz.Title = req.Title
	}
	if req.Description != "" {
		quiz.Description = req.Description
	}
	if req.TimeLimit != nil {
		quiz.TimeLimit = *req.TimeLimit
	}
	if req.PassScore != nil {
		quiz.PassScore = *req.PassScore
	}
	if req.IsPublished != nil {
		quiz.IsPublished = *req.IsPublished
	}
	if req.TotalQuestions != nil && *req.TotalQuestions > 0 {
		quiz.TotalQuestions = *req.TotalQuestions
	}

	if err := database.DB.Save(&quiz).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"quiz": quiz})
}

func DeleteQuiz(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var quiz models.Quiz
	if err := database.DB.Preload("Course").First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && quiz.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := database.DB.Delete(&quiz).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quiz"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quiz deleted successfully"})
}

func StartQuizAttempt(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var quiz models.Quiz
	if err := database.DB.First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	if !quiz.IsPublished {
		c.JSON(http.StatusForbidden, gin.H{"error": "Quiz not available"})
		return
	}

	userID, _ := c.Get("user_id")
	attempt := models.QuizAttempt{
		UserID:    userID.(uint),
		QuizID:    uint(id),
		StartedAt: time.Now(),
	}

	if err := database.DB.Create(&attempt).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start quiz attempt"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"attempt": attempt})
}

func SubmitQuizAttempt(c *gin.Context) {
	attemptID, err := strconv.ParseUint(c.Param("attempt_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid attempt ID"})
		return
	}

	var attempt models.QuizAttempt
	if err := database.DB.Preload("Quiz.Questions.Options").
		First(&attempt, attemptID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz attempt not found"})
		return
	}

	// Check if this is the user's attempt
	userID, _ := c.Get("user_id")
	if attempt.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req SubmitQuizRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Calculate max score from all questions in the quiz
	var maxScore float64
	for _, question := range attempt.Quiz.Questions {
		maxScore += question.Points
	}

	// Calculate score and save answers
	var totalScore float64
	for _, answer := range req.Answers {
		// Find the question
		var question models.Question
		for _, q := range attempt.Quiz.Questions {
			if q.ID == answer.QuestionID {
				question = q
				break
			}
		}

		// Save user answers
		for _, optionID := range answer.OptionIDs {
			userAnswer := models.Answer{
				QuizAttemptID: attempt.ID,
				QuestionID:    answer.QuestionID,
				OptionID:      optionID,
			}
			database.DB.Create(&userAnswer)
		}

		// Calculate score for this question
		if question.QuestionType == models.QuestionTypeSingleChoice {
			// For single choice, check if the selected option is correct
			for _, optionID := range answer.OptionIDs {
				for _, option := range question.Options {
					if option.ID == optionID && option.IsCorrect {
						totalScore += question.Points
						break
					}
				}
			}
		} else if question.QuestionType == models.QuestionTypeMultipleChoice {
			// For multiple choice, calculate partial credit
			totalCorrectOptions := 0
			selectedCorrectOptions := 0
			selectedIncorrectOptions := 0

			// Count total correct options for this question
			for _, option := range question.Options {
				if option.IsCorrect {
					totalCorrectOptions++
				}
			}

			// Count selected correct and incorrect options
			for _, optionID := range answer.OptionIDs {
				for _, option := range question.Options {
					if option.ID == optionID {
						if option.IsCorrect {
							selectedCorrectOptions++
						} else {
							selectedIncorrectOptions++
						}
						break
					}
				}
			}

			// Award points only if all correct options are selected and no incorrect ones
			if selectedCorrectOptions == totalCorrectOptions && selectedIncorrectOptions == 0 {
				totalScore += question.Points
			}
		}
	}

	// Update attempt with results
	var percentage float64
	if maxScore > 0 {
		percentage = (totalScore / maxScore) * 100
	}
	attempt.Score = totalScore
	attempt.MaxScore = maxScore
	attempt.Percentage = percentage
	attempt.IsPassed = percentage >= attempt.Quiz.PassScore
	attempt.CompletedAt = time.Now()

	if err := database.DB.Save(&attempt).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save quiz results"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"attempt": attempt})
}
