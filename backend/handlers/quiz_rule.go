package handlers

import (
	"net/http"
	"strconv"

	"etuto-backend/database"

	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

// Quiz Rule handlers

type CreateQuizRuleRequest struct {
	CategoryID   uint                   `json:"category_id" binding:"required"`
	Difficulty   models.DifficultyLevel `json:"difficulty" binding:"required"`
	QuestionType models.QuestionType    `json:"question_type" binding:"required"`
	Percentage   float64                `json:"percentage" binding:"required,min=0.1,max=100"`
	OrderIndex   int                    `json:"order_index" binding:"required"`
}

type UpdateQuizRuleRequest struct {
	CategoryID   uint                   `json:"category_id"`
	Difficulty   models.DifficultyLevel `json:"difficulty"`
	QuestionType models.QuestionType    `json:"question_type"`
	Percentage   float64                `json:"percentage" binding:"min=0.1,max=100"`
	OrderIndex   int                    `json:"order_index"`
}

func GetQuizRules(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var rules []models.QuizRule
	if err := database.DB.Where("quiz_id = ?", uint(quizID)).
		Preload("Category").
		Order("order_index").
		Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz rules"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"rules": rules})
}

func GetQuizRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	var rule models.QuizRule
	if err := database.DB.Preload("Category").First(&rule, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Rule not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"rule": rule})
}

func CreateQuizRule(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var req CreateQuizRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if quiz exists
	var quiz models.Quiz
	if err := database.DB.First(&quiz, uint(quizID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check if category exists
	var category models.QuestionBankCategory
	if err := database.DB.First(&category, req.CategoryID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Validate that there are questions matching the criteria
	var questionCount int64
	database.DB.Model(&models.BankQuestion{}).
		Where("category_id = ? AND difficulty = ? AND question_type = ?",
			req.CategoryID, req.Difficulty, req.QuestionType).
		Count(&questionCount)

	if questionCount == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No questions found matching the specified criteria"})
		return
	}

	// Use the quiz already loaded above to calculate count from percentage

	// Calculate count from percentage for validation
	calculatedCount := int(float64(quiz.TotalQuestions) * req.Percentage / 100.0)
	if calculatedCount < 1 {
		calculatedCount = 1 // Minimum 1 question
	}

	if calculatedCount > int(questionCount) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":      "Calculated count from percentage exceeds available questions",
			"available":  questionCount,
			"calculated": calculatedCount,
			"percentage": req.Percentage,
		})
		return
	}

	rule := models.QuizRule{
		QuizID:       uint(quizID),
		CategoryID:   req.CategoryID,
		Difficulty:   req.Difficulty,
		QuestionType: req.QuestionType,
		Percentage:   req.Percentage,
		OrderIndex:   req.OrderIndex,
	}

	if err := database.DB.Create(&rule).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz rule"})
		return
	}

	// Load relationships
	database.DB.Preload("Category").First(&rule, rule.ID)

	c.JSON(http.StatusCreated, gin.H{"rule": rule})
}

func UpdateQuizRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	var req UpdateQuizRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rule models.QuizRule
	if err := database.DB.First(&rule, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Rule not found"})
		return
	}

	// Update fields
	if req.CategoryID != 0 {
		rule.CategoryID = req.CategoryID
	}
	if req.Difficulty != "" {
		rule.Difficulty = req.Difficulty
	}
	if req.QuestionType != "" {
		rule.QuestionType = req.QuestionType
	}
	if req.Percentage > 0 {
		rule.Percentage = req.Percentage
	}
	if req.OrderIndex != 0 {
		rule.OrderIndex = req.OrderIndex
	}

	// Validate updated criteria
	if req.CategoryID != 0 || req.Difficulty != "" || req.QuestionType != "" || req.Percentage > 0 {
		var questionCount int64
		database.DB.Model(&models.BankQuestion{}).
			Where("category_id = ? AND difficulty = ? AND question_type = ?",
				rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Count(&questionCount)

		if questionCount == 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No questions found matching the updated criteria"})
			return
		}

		// Get quiz to calculate count for validation
		var quiz models.Quiz
		if err := database.DB.First(&quiz, rule.QuizID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
			return
		}

		calculatedCount := rule.GetCalculatedCount(quiz.TotalQuestions)
		if calculatedCount > int(questionCount) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":      "Calculated count from percentage exceeds available questions",
				"available":  questionCount,
				"calculated": calculatedCount,
				"percentage": rule.Percentage,
			})
			return
		}
	}

	if err := database.DB.Save(&rule).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz rule"})
		return
	}

	// Load relationships
	database.DB.Preload("Category").First(&rule, rule.ID)

	c.JSON(http.StatusOK, gin.H{"rule": rule})
}

func DeleteQuizRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	if err := database.DB.Delete(&models.QuizRule{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quiz rule"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quiz rule deleted successfully"})
}

// Helper function to preview questions that would be selected by rules
func PreviewQuizQuestions(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var rules []models.QuizRule
	if err := database.DB.Where("quiz_id = ?", uint(quizID)).
		Preload("Category").
		Order("order_index").
		Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz rules"})
		return
	}

	type PreviewResult struct {
		Rule               models.QuizRule       `json:"rule"`
		AvailableQuestions int64                 `json:"available_questions"`
		SampleQuestions    []models.BankQuestion `json:"sample_questions"`
	}

	var preview []PreviewResult

	for _, rule := range rules {
		var questionCount int64
		database.DB.Model(&models.BankQuestion{}).
			Where("category_id = ? AND difficulty = ? AND question_type = ?",
				rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Count(&questionCount)

		var sampleQuestions []models.BankQuestion
		database.DB.Where("category_id = ? AND difficulty = ? AND question_type = ?",
			rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Preload("Options").
			Limit(3). // Show max 3 sample questions
			Find(&sampleQuestions)

		preview = append(preview, PreviewResult{
			Rule:               rule,
			AvailableQuestions: questionCount,
			SampleQuestions:    sampleQuestions,
		})
	}

	c.JSON(http.StatusOK, gin.H{"preview": preview})
}
