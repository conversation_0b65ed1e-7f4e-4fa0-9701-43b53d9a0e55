package handlers

import (
	"etuto-database/models"
	"net/http"
	"strconv"

	"etuto-backend/database"

	"github.com/gin-gonic/gin"
)

type CreateSlideRequest struct {
	LessonID   uint   `json:"lesson_id" binding:"required"`
	Title      string `json:"title"`
	Content    string `json:"content" binding:"required"`
	ImageURL   string `json:"image_url"`
	OrderIndex int    `json:"order_index" binding:"required"`
}

type UpdateSlideRequest struct {
	Title      string `json:"title"`
	Content    string `json:"content"`
	ImageURL   string `json:"image_url"`
	OrderIndex *int   `json:"order_index"`
}

func GetSlidesByLesson(c *gin.Context) {
	lessonID, err := strconv.ParseUint(c.Param("lesson_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	var slides []models.Slide
	if err := database.DB.Where("lesson_id = ?", lessonID).
		Order("order_index").
		Find(&slides).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch slides"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"slides": slides})
}

func GetSlide(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid slide ID"})
		return
	}

	var slide models.Slide
	if err := database.DB.Preload("Lesson.Course").
		First(&slide, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Slide not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"slide": slide})
}

func CreateSlide(c *gin.Context) {
	var req CreateSlideRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if lesson exists and user has permission
	var lesson models.Lesson
	if err := database.DB.Preload("Course").First(&lesson, req.LessonID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && lesson.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	slide := models.Slide{
		LessonID:   req.LessonID,
		Title:      req.Title,
		Content:    req.Content,
		ImageURL:   req.ImageURL,
		OrderIndex: req.OrderIndex,
	}

	if err := database.DB.Create(&slide).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create slide"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"slide": slide})
}

func UpdateSlide(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid slide ID"})
		return
	}

	var slide models.Slide
	if err := database.DB.Preload("Lesson.Course").First(&slide, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Slide not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && slide.Lesson.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req UpdateSlideRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields
	if req.Title != "" {
		slide.Title = req.Title
	}
	if req.Content != "" {
		slide.Content = req.Content
	}
	if req.ImageURL != "" {
		slide.ImageURL = req.ImageURL
	}
	if req.OrderIndex != nil {
		slide.OrderIndex = *req.OrderIndex
	}

	if err := database.DB.Save(&slide).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update slide"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"slide": slide})
}

func DeleteSlide(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid slide ID"})
		return
	}

	var slide models.Slide
	if err := database.DB.Preload("Lesson.Course").First(&slide, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Slide not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && slide.Lesson.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := database.DB.Delete(&slide).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete slide"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Slide deleted successfully"})
}
