package handlers

import (
	"net/http"
	"strconv"

	"etuto-backend/database"

	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

func EnrollInCourse(c *gin.Context) {
	courseID, err := strconv.ParseUint(c.<PERSON>m("course_id"), 10, 32)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	userID, _ := c.Get("user_id")

	// Check if course exists and is published
	var course models.Course
	if err := database.DB.First(&course, courseID).Error; err != nil {
		c.J<PERSON>N(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	if !course.IsPublished {
		c.JSON(http.StatusForbidden, gin.H{"error": "Course is not available for enrollment"})
		return
	}

	// Check if already enrolled
	var existingEnrollment models.Enrollment
	if err := database.DB.Where("user_id = ? AND course_id = ?", userID, courseID).
		First(&existingEnrollment).Error; err == nil {
		c.<PERSON>(http.StatusConflict, gin.H{"error": "Already enrolled in this course"})
		return
	}

	// Create enrollment
	enrollment := models.Enrollment{
		UserID:   userID.(uint),
		CourseID: uint(courseID),
		Progress: 0,
	}

	if err := database.DB.Create(&enrollment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to enroll in course"})
		return
	}

	// Load course information
	database.DB.Preload("Course").First(&enrollment, enrollment.ID)

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Successfully enrolled in course",
		"enrollment": enrollment,
	})
}

func GetMyEnrollments(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var enrollments []models.Enrollment
	if err := database.DB.Where("user_id = ?", userID).
		Preload("Course.Creator").
		Preload("Course.Lessons").
		Find(&enrollments).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch enrollments"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"enrollments": enrollments})
}
