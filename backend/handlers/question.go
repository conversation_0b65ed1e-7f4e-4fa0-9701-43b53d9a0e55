package handlers

import (
	"net/http"
	"strconv"

	"etuto-backend/database"

	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

type CreateQuestionRequest struct {
	QuizID       uint                  `json:"quiz_id" binding:"required"`
	QuestionText string                `json:"question_text" binding:"required"`
	QuestionType models.QuestionType   `json:"question_type" binding:"required"`
	ImageURL     string                `json:"image_url"`
	Points       float64               `json:"points"`
	OrderIndex   int                   `json:"order_index" binding:"required"`
	Options      []CreateOptionRequest `json:"options" binding:"required,min=2"`
}

type CreateOptionRequest struct {
	OptionText string `json:"option_text" binding:"required"`
	IsCorrect  bool   `json:"is_correct"`
	OrderIndex int    `json:"order_index" binding:"required"`
}

type UpdateQuestionRequest struct {
	QuestionText *string               `json:"question_text"`
	QuestionType *models.QuestionType  `json:"question_type"`
	ImageURL     *string               `json:"image_url"`
	Points       *float64              `json:"points"`
	OrderIndex   *int                  `json:"order_index"`
	Options      []UpdateOptionRequest `json:"options"`
}

type UpdateOptionRequest struct {
	ID         uint    `json:"id"`
	OptionText *string `json:"option_text"`
	IsCorrect  *bool   `json:"is_correct"`
	OrderIndex *int    `json:"order_index"`
}

func GetQuestionsByQuiz(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var questions []models.Question
	if err := database.DB.Where("quiz_id = ?", quizID).
		Preload("Options").
		Order("order_index").
		Find(&questions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch questions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"questions": questions})
}

func GetQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	var question models.Question
	if err := database.DB.Preload("Options").First(&question, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"question": question})
}

func CreateQuestion(c *gin.Context) {
	var req CreateQuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Verify quiz exists and user has permission
	var quiz models.Quiz
	if err := database.DB.First(&quiz, req.QuizID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check if user can modify this quiz
	userRole, _ := c.Get("user_role")
	userID, _ := c.Get("user_id")
	if userRole != models.RoleAdmin {
		var course models.Course
		if err := database.DB.First(&course, quiz.CourseID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify permissions"})
			return
		}
		if course.CreatorID != userID.(uint) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
			return
		}
	}

	// Validate that at least one option is correct
	hasCorrectOption := false
	for _, option := range req.Options {
		if option.IsCorrect {
			hasCorrectOption = true
			break
		}
	}
	if !hasCorrectOption {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one option must be correct"})
		return
	}

	// Create question
	question := models.Question{
		QuizID:       req.QuizID,
		QuestionText: req.QuestionText,
		QuestionType: req.QuestionType,
		ImageURL:     req.ImageURL,
		Points:       req.Points,
		OrderIndex:   req.OrderIndex,
	}

	if err := database.DB.Create(&question).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create question"})
		return
	}

	// Create options
	for _, optionReq := range req.Options {
		option := models.Option{
			QuestionID: question.ID,
			OptionText: optionReq.OptionText,
			IsCorrect:  optionReq.IsCorrect,
			OrderIndex: optionReq.OrderIndex,
		}
		if err := database.DB.Create(&option).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create option"})
			return
		}
	}

	// Reload question with options
	database.DB.Preload("Options").First(&question, question.ID)

	c.JSON(http.StatusCreated, gin.H{"question": question})
}

func UpdateQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	var req UpdateQuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get existing question
	var question models.Question
	if err := database.DB.Preload("Quiz").First(&question, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	// Check permissions
	userRole, _ := c.Get("user_role")
	userID, _ := c.Get("user_id")
	if userRole != models.RoleAdmin {
		var course models.Course
		if err := database.DB.First(&course, question.Quiz.CourseID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify permissions"})
			return
		}
		if course.CreatorID != userID.(uint) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
			return
		}
	}

	// Update question fields
	if req.QuestionText != nil {
		question.QuestionText = *req.QuestionText
	}
	if req.QuestionType != nil {
		question.QuestionType = *req.QuestionType
	}
	if req.ImageURL != nil {
		question.ImageURL = *req.ImageURL
	}
	if req.Points != nil {
		question.Points = *req.Points
	}
	if req.OrderIndex != nil {
		question.OrderIndex = *req.OrderIndex
	}

	if err := database.DB.Save(&question).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update question"})
		return
	}

	// Update options if provided
	if req.Options != nil {
		// Delete existing options
		database.DB.Where("question_id = ?", question.ID).Delete(&models.Option{})

		// Create new options
		for _, optionReq := range req.Options {
			option := models.Option{
				QuestionID: question.ID,
				OptionText: *optionReq.OptionText,
				IsCorrect:  *optionReq.IsCorrect,
				OrderIndex: *optionReq.OrderIndex,
			}
			if err := database.DB.Create(&option).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update options"})
				return
			}
		}
	}

	// Reload question with options
	database.DB.Preload("Options").First(&question, question.ID)

	c.JSON(http.StatusOK, gin.H{"question": question})
}

func DeleteQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	// Get existing question
	var question models.Question
	if err := database.DB.Preload("Quiz").First(&question, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	// Check permissions
	userRole, _ := c.Get("user_role")
	userID, _ := c.Get("user_id")
	if userRole != models.RoleAdmin {
		var course models.Course
		if err := database.DB.First(&course, question.Quiz.CourseID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify permissions"})
			return
		}
		if course.CreatorID != userID.(uint) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
			return
		}
	}

	// Delete options first (cascade)
	database.DB.Where("question_id = ?", question.ID).Delete(&models.Option{})

	// Delete question
	if err := database.DB.Delete(&question).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete question"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Question deleted successfully"})
}
