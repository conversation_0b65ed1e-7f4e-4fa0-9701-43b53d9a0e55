package handlers

import (
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"etuto-backend/database"

	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

// GenerateQuizQuestions generates questions for a quiz attempt based on quiz rules
func GenerateQuizQuestions(c *gin.Context) {
	quizAttemptID, err := strconv.ParseUint(c.Param("attempt_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz attempt ID"})
		return
	}

	// Get quiz attempt
	var attempt models.QuizAttempt
	if err := database.DB.Preload("Quiz").First(&attempt, uint(quizAttemptID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz attempt not found"})
		return
	}

	// Check if questions already generated for this attempt
	var existingCount int64
	database.DB.Model(&models.QuizAttemptQuestion{}).Where("quiz_attempt_id = ?", uint(quizAttemptID)).Count(&existingCount)
	if existingCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Questions already generated for this attempt"})
		return
	}

	// Get quiz rules or generate them automatically
	var rules []models.QuizRule
	if err := database.DB.Where("quiz_id = ?", attempt.QuizID).
		Preload("Category").
		Order("order_index").
		Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz rules"})
		return
	}

	// If no rules exist, generate them automatically based on total_questions
	if len(rules) == 0 {
		generatedRules, err := generateAutoRules(attempt.Quiz)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		rules = generatedRules
	}

	// Start transaction
	tx := database.DB.Begin()

	var allQuestions []models.QuizAttemptQuestion
	orderIndex := 1

	// Generate questions for each rule
	for _, rule := range rules {
		// Get available questions for this rule
		var availableQuestions []models.BankQuestion
		if err := tx.Where("category_id = ? AND difficulty = ? AND question_type = ?",
			rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Preload("Options").
			Find(&availableQuestions).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch questions for rule"})
			return
		}

		// Calculate count from percentage
		calculatedCount := rule.GetCalculatedCount(attempt.Quiz.TotalQuestions)

		if len(availableQuestions) < calculatedCount {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"error":     "Not enough questions available for rule",
				"rule_id":   rule.ID,
				"available": len(availableQuestions),
				"required":  calculatedCount,
			})
			return
		}

		// Randomly select questions
		selectedQuestions := selectRandomQuestions(availableQuestions, calculatedCount)

		// Create QuizAttemptQuestion records
		for _, question := range selectedQuestions {
			attemptQuestion := models.QuizAttemptQuestion{
				QuizAttemptID:  uint(quizAttemptID),
				BankQuestionID: question.ID,
				OrderIndex:     orderIndex,
				Points:         question.Points, // Use question's default points
			}

			if err := tx.Create(&attemptQuestion).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create attempt question"})
				return
			}

			allQuestions = append(allQuestions, attemptQuestion)
			orderIndex++
		}
	}

	tx.Commit()

	// Load full data for response
	var generatedQuestions []models.QuizAttemptQuestion
	database.DB.Where("quiz_attempt_id = ?", uint(quizAttemptID)).
		Preload("BankQuestion.Category").
		Preload("BankQuestion.Options").
		Order("order_index").
		Find(&generatedQuestions)

	c.JSON(http.StatusOK, gin.H{
		"message":         "Questions generated successfully",
		"questions":       generatedQuestions,
		"total_questions": len(generatedQuestions),
	})
}

// GetQuizAttemptQuestions returns questions for a specific quiz attempt
func GetQuizAttemptQuestions(c *gin.Context) {
	quizAttemptID, err := strconv.ParseUint(c.Param("attempt_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz attempt ID"})
		return
	}

	var questions []models.QuizAttemptQuestion
	if err := database.DB.Where("quiz_attempt_id = ?", uint(quizAttemptID)).
		Preload("BankQuestion.Category").
		Preload("BankQuestion.Options").
		Order("order_index").
		Find(&questions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch attempt questions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"questions": questions})
}

// Helper function to randomly select questions
func selectRandomQuestions(questions []models.BankQuestion, count int) []models.BankQuestion {
	if len(questions) <= count {
		return questions
	}

	// Create a copy to avoid modifying original slice
	available := make([]models.BankQuestion, len(questions))
	copy(available, questions)

	// Shuffle using Fisher-Yates algorithm
	rand.Seed(time.Now().UnixNano())
	for i := len(available) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		available[i], available[j] = available[j], available[i]
	}

	return available[:count]
}

// generateAutoRules automatically creates quiz rules based on total_questions
func generateAutoRules(quiz models.Quiz) ([]models.QuizRule, error) {
	// Get all available categories
	var categories []models.QuestionBankCategory
	if err := database.DB.Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch categories")
	}

	if len(categories) == 0 {
		return nil, fmt.Errorf("no question categories available")
	}

	totalQuestions := quiz.TotalQuestions
	if totalQuestions <= 0 {
		totalQuestions = 10 // Default
	}

	var rules []models.QuizRule

	// Distribution strategy based on difficulty
	var easyPercent, normalPercent float64
	switch quiz.Difficulty {
	case models.DifficultyEasy:
		easyPercent, normalPercent = 0.6, 0.3
	case models.DifficultyNormal:
		easyPercent, normalPercent = 0.3, 0.5
	case models.DifficultyHard:
		easyPercent, normalPercent = 0.2, 0.3
	default:
		easyPercent, normalPercent = 0.3, 0.5
	}

	// Calculate questions per difficulty
	easyCount := int(float64(totalQuestions) * easyPercent)
	normalCount := int(float64(totalQuestions) * normalPercent)
	hardCount := totalQuestions - easyCount - normalCount // Remaining questions

	// Ensure at least 1 question per difficulty if total > 3
	if totalQuestions >= 3 {
		if easyCount == 0 {
			easyCount = 1
		}
		if normalCount == 0 {
			normalCount = 1
		}
		if hardCount == 0 {
			hardCount = 1
		}

		// Adjust if total exceeds
		total := easyCount + normalCount + hardCount
		if total > totalQuestions {
			diff := total - totalQuestions
			if hardCount > diff {
				hardCount -= diff
			} else if normalCount > diff {
				normalCount -= diff
			} else {
				easyCount -= diff
			}
		}
	}

	orderIndex := 1

	// Distribute questions across categories
	questionsPerDifficulty := []struct {
		difficulty models.DifficultyLevel
		count      int
	}{
		{models.DifficultyEasy, easyCount},
		{models.DifficultyNormal, normalCount},
		{models.DifficultyHard, hardCount},
	}

	for _, difficultyGroup := range questionsPerDifficulty {
		if difficultyGroup.count == 0 {
			continue
		}

		// Distribute questions across categories for this difficulty
		questionsPerCategory := difficultyGroup.count / len(categories)
		remainder := difficultyGroup.count % len(categories)

		for i, category := range categories {
			count := questionsPerCategory
			if i < remainder {
				count++ // Distribute remainder
			}

			if count > 0 {
				// Check if questions are available for this combination
				var availableCount int64
				database.DB.Model(&models.BankQuestion{}).
					Where("category_id = ? AND difficulty = ?", category.ID, difficultyGroup.difficulty).
					Count(&availableCount)

				if availableCount > 0 {
					// Use minimum of requested count and available count
					finalCount := count
					if int64(finalCount) > availableCount {
						finalCount = int(availableCount)
					}

					// Prefer single choice for easier questions, mix for harder
					questionType := models.QuestionTypeSingleChoice
					if difficultyGroup.difficulty == models.DifficultyHard && i%2 == 1 {
						questionType = models.QuestionTypeMultipleChoice
					}

					// Calculate percentage for this rule
					percentage := float64(finalCount) / float64(totalQuestions) * 100.0

					rule := models.QuizRule{
						QuizID:       quiz.ID,
						CategoryID:   category.ID,
						Difficulty:   difficultyGroup.difficulty,
						QuestionType: questionType,
						Percentage:   percentage,
						OrderIndex:   orderIndex,
					}
					rules = append(rules, rule)
					orderIndex++
				}
			}
		}
	}

	if len(rules) == 0 {
		return nil, fmt.Errorf("no questions available for auto-generation")
	}

	return rules, nil
}

// ValidateQuizRules checks if quiz rules are valid and can generate questions
func ValidateQuizRules(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var rules []models.QuizRule
	if err := database.DB.Where("quiz_id = ?", uint(quizID)).
		Preload("Category").
		Order("order_index").
		Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz rules"})
		return
	}

	// Get quiz to calculate counts
	var quiz models.Quiz
	if err := database.DB.First(&quiz, uint(quizID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	type ValidationResult struct {
		RuleID             uint   `json:"rule_id"`
		Valid              bool   `json:"valid"`
		AvailableQuestions int64  `json:"available_questions"`
		RequiredQuestions  int    `json:"required_questions"`
		Error              string `json:"error,omitempty"`
	}

	var results []ValidationResult
	var totalQuestions int
	var allValid = true

	for _, rule := range rules {
		var questionCount int64
		database.DB.Model(&models.BankQuestion{}).
			Where("category_id = ? AND difficulty = ? AND question_type = ?",
				rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Count(&questionCount)

		// Calculate required questions from percentage
		requiredQuestions := rule.GetCalculatedCount(quiz.TotalQuestions)

		result := ValidationResult{
			RuleID:             rule.ID,
			AvailableQuestions: questionCount,
			RequiredQuestions:  requiredQuestions,
			Valid:              questionCount >= int64(requiredQuestions),
		}

		if !result.Valid {
			result.Error = "Not enough questions available"
			allValid = false
		}

		results = append(results, result)
		totalQuestions += requiredQuestions
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":            allValid,
		"total_questions":  totalQuestions,
		"rules_validation": results,
	})
}
