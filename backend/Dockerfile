# Development Dockerfile for Go backend API
FROM golang:1.24.1-alpine as builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN go build -o etuto-backend .

FROM alpine:latest

WORKDIR /app

# Copy the built application from the previous stage
COPY --from=builder /app/etuto-backend .

EXPOSE 8080

# Start the application
CMD ["./etuto-backend"]
