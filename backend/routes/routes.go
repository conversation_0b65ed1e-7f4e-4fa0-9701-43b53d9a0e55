package routes

import (
	"etuto-backend/handlers"
	"etuto-backend/middleware"

	"github.com/gin-gonic/gin"
)

func SetupRoutes(r *gin.Engine) {
	// Health check
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	api := r.Group("/api")
	{
		// Auth routes (public)
		auth := api.Group("/auth")
		{
			auth.POST("/login", handlers.Login)
			auth.POST("/register", handlers.Register)
		}

		// Protected routes
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware())
		{
			// Profile
			protected.GET("/profile", handlers.GetProfile)

			// Courses
			courses := protected.Group("/courses")
			{
				courses.GET("", handlers.GetCourses) // Handle /api/courses
				courses.GET("/:id", handlers.GetCourse)

				// Teacher/Admin only
				courses.POST("", middleware.RequireTeacherOrAdmin(), handlers.CreateCourse) // Handle /api/courses
				courses.PUT("/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateCourse)
				courses.DELETE("/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteCourse)
			}

			// Lessons (will be implemented)
			lessons := protected.Group("/lessons")
			{
				lessons.GET("/course/:course_id", handlers.GetLessonsByCourse)
				lessons.GET("/:id", handlers.GetLesson)
				lessons.POST("", middleware.RequireTeacherOrAdmin(), handlers.CreateLesson)  // Handle /api/lessons
				lessons.POST("/", middleware.RequireTeacherOrAdmin(), handlers.CreateLesson) // Handle /api/lessons/
				lessons.PUT("/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateLesson)
				lessons.DELETE("/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteLesson)
			}

			// Slides (will be implemented)
			slides := protected.Group("/slides")
			{
				slides.GET("/lesson/:lesson_id", handlers.GetSlidesByLesson)
				slides.GET("/:id", handlers.GetSlide)
				slides.POST("", middleware.RequireTeacherOrAdmin(), handlers.CreateSlide)  // Handle /api/slides
				slides.POST("/", middleware.RequireTeacherOrAdmin(), handlers.CreateSlide) // Handle /api/slides/
				slides.PUT("/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateSlide)
				slides.DELETE("/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteSlide)
			}

			// Quizzes
			quizzes := protected.Group("/quizzes")
			{
				quizzes.GET("/course/:course_id", handlers.GetQuizzesByCourse)
				quizzes.GET("/:id", handlers.GetQuiz)
				quizzes.POST("", middleware.RequireTeacherOrAdmin(), handlers.CreateQuiz)  // Handle /api/quizzes
				quizzes.POST("/", middleware.RequireTeacherOrAdmin(), handlers.CreateQuiz) // Handle /api/quizzes/
				quizzes.PUT("/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateQuiz)
				quizzes.DELETE("/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteQuiz)

				// Quiz attempts
				quizzes.POST("/:id/attempt", handlers.StartQuizAttempt)
				quizzes.POST("/attempt/:attempt_id/submit", handlers.SubmitQuizAttempt)
			}

			// Questions
			questions := protected.Group("/questions")
			{
				questions.GET("/quiz/:quiz_id", handlers.GetQuestionsByQuiz)
				questions.GET("/:id", handlers.GetQuestion)
				questions.POST("", middleware.RequireTeacherOrAdmin(), handlers.CreateQuestion)  // Handle /api/questions
				questions.POST("/", middleware.RequireTeacherOrAdmin(), handlers.CreateQuestion) // Handle /api/questions/
				questions.PUT("/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateQuestion)
				questions.DELETE("/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteQuestion)
			}

			// Question Bank
			questionBank := protected.Group("/question-bank")
			{
				// Categories
				questionBank.GET("/categories", handlers.GetQuestionBankCategories)
				questionBank.GET("/categories/:id", handlers.GetQuestionBankCategory)
				questionBank.POST("/categories", middleware.RequireTeacherOrAdmin(), handlers.CreateQuestionBankCategory)
				questionBank.PUT("/categories/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateQuestionBankCategory)
				questionBank.DELETE("/categories/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteQuestionBankCategory)

				// Bank Questions
				questionBank.GET("/questions", handlers.GetBankQuestions)
				questionBank.GET("/questions/:id", handlers.GetBankQuestion)
				questionBank.POST("/questions", middleware.RequireTeacherOrAdmin(), handlers.CreateBankQuestion)
				questionBank.PUT("/questions/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateBankQuestion)
				questionBank.DELETE("/questions/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteBankQuestion)

				// Quiz Rules (dynamic question generation rules)
				questionBank.GET("/quiz/:quiz_id/rules", handlers.GetQuizRules)
				questionBank.GET("/quiz-rules/:id", handlers.GetQuizRule)
				questionBank.POST("/quiz/:quiz_id/rules", middleware.RequireTeacherOrAdmin(), handlers.CreateQuizRule)
				questionBank.PUT("/quiz-rules/:id", middleware.RequireTeacherOrAdmin(), handlers.UpdateQuizRule)
				questionBank.DELETE("/quiz-rules/:id", middleware.RequireTeacherOrAdmin(), handlers.DeleteQuizRule)
				questionBank.GET("/quiz/:quiz_id/preview", handlers.PreviewQuizQuestions)
				questionBank.GET("/quiz/:quiz_id/validate", handlers.ValidateQuizRules)

				// Quiz Generation (dynamic question generation for attempts)
				questionBank.POST("/quiz-attempt/:attempt_id/generate", handlers.GenerateQuizQuestions)
				questionBank.GET("/quiz-attempt/:attempt_id/questions", handlers.GetQuizAttemptQuestions)
			}

			// Progress tracking (will be implemented)
			progress := protected.Group("/progress")
			{
				progress.GET("/course/:course_id", handlers.GetCourseProgress)
				progress.POST("/slide/:slide_id/complete", handlers.MarkSlideComplete)
			}

			// Enrollments (will be implemented)
			enrollments := protected.Group("/enrollments")
			{
				enrollments.POST("/course/:course_id", handlers.EnrollInCourse)
				enrollments.GET("/my-courses", handlers.GetMyEnrollments)
			}

			// Admin routes
			admin := protected.Group("/admin")
			admin.Use(middleware.RequireAdmin())
			{
				admin.GET("/users", handlers.GetUsers)
				admin.POST("/users", handlers.CreateUser)
				admin.PUT("/users/:id", handlers.UpdateUser)
				admin.DELETE("/users/:id", handlers.DeleteUser)
				admin.PUT("/users/:id/role", handlers.UpdateUserRole)
				admin.PUT("/users/:id/status", handlers.UpdateUserStatus)
			}
		}
	}
}
