import { useState, useEffect } from 'react';
import { slideService } from '../services/slideService';
import { type Slide, type Lesson } from '../types';

interface UseSlidesState {
  slides: Slide[];
  loading: boolean;
  error: string | null;
}

interface UseSlidesReturn extends UseSlidesState {
  refetch: () => Promise<void>;
  markSlideComplete: (slideId: number) => Promise<void>;
}

export const useSlides = (lessonId: number): UseSlidesReturn => {
  const [state, setState] = useState<UseSlidesState>({
    slides: [],
    loading: true,
    error: null,
  });

  const fetchSlides = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const slides = await slideService.getSlidesByLesson(lessonId);
      setState(prev => ({ ...prev, slides, loading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch slides';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
    }
  };

  const markSlideComplete = async (slideId: number) => {
    try {
      await slideService.markSlideComplete(slideId);
    } catch (error) {
      console.error('Error marking slide complete:', error);
    }
  };

  useEffect(() => {
    if (lessonId) {
      fetchSlides();
    }
  }, [lessonId]);

  return {
    ...state,
    refetch: fetchSlides,
    markSlideComplete,
  };
};

// Hook for fetching a single lesson with slides
interface UseLessonState {
  lesson: Lesson | null;
  loading: boolean;
  error: string | null;
}

interface UseLessonReturn extends UseLessonState {
  refetch: () => Promise<void>;
}

export const useLesson = (lessonId: number): UseLessonReturn => {
  const [state, setState] = useState<UseLessonState>({
    lesson: null,
    loading: true,
    error: null,
  });

  const fetchLesson = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const lesson = await slideService.getLesson(lessonId);
      setState(prev => ({ ...prev, lesson, loading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch lesson';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
    }
  };

  useEffect(() => {
    if (lessonId) {
      fetchLesson();
    }
  }, [lessonId]);

  return {
    ...state,
    refetch: fetchLesson,
  };
};
