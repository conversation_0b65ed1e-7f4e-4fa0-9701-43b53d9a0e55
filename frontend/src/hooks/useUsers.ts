import { useState, useEffect } from 'react';
import { type User } from '../types';
import { userService, type CreateUserRequest, type UpdateUserRequest } from '../services/userService';

export const useUsers = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const fetchedUsers = await userService.getUsers();
      setUsers(fetchedUsers);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const createUser = async (userData: CreateUserRequest): Promise<User> => {
    try {
      setError(null);
      const newUser = await userService.createUser(userData);
      setUsers(prev => [...prev, newUser]);
      return newUser;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to create user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateUser = async (userId: number, userData: UpdateUserRequest): Promise<User> => {
    try {
      setError(null);
      const updatedUser = await userService.updateUser(userId, userData);
      setUsers(prev => prev.map(user => user.id === userId ? updatedUser : user));
      return updatedUser;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to update user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteUser = async (userId: number): Promise<void> => {
    try {
      setError(null);
      await userService.deleteUser(userId);
      setUsers(prev => prev.filter(user => user.id !== userId));
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to delete user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateUserRole = async (userId: number, role: 'admin' | 'teacher' | 'student'): Promise<User> => {
    try {
      setError(null);
      const updatedUser = await userService.updateUserRole(userId, role);
      setUsers(prev => prev.map(user => user.id === userId ? updatedUser : user));
      return updatedUser;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to update user role';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateUserStatus = async (userId: number, isActive: boolean): Promise<User> => {
    try {
      setError(null);
      const updatedUser = await userService.updateUserStatus(userId, isActive);
      setUsers(prev => prev.map(user => user.id === userId ? updatedUser : user));
      return updatedUser;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to update user status';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return {
    users,
    loading,
    error,
    refetch: fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    updateUserRole,
    updateUserStatus,
  };
};
