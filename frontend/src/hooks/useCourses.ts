import { useState, useEffect } from 'react';
import { courseService } from '../services/courseService';
import { type Course } from '../types';

interface UseCoursesState {
  courses: Course[];
  loading: boolean;
  error: string | null;
}

interface UseCoursesReturn extends UseCoursesState {
  refetch: () => Promise<void>;
}

export const useCourses = (): UseCoursesReturn => {
  const [state, setState] = useState<UseCoursesState>({
    courses: [],
    loading: true,
    error: null,
  });

  const fetchCourses = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const courses = await courseService.getCourses();
      setState(prev => ({ ...prev, courses, loading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch courses';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  return {
    ...state,
    refetch: fetchCourses,
  };
};

// Hook for fetching a single course
interface UseCourseState {
  course: Course | null;
  loading: boolean;
  error: string | null;
}

interface UseCourseReturn extends UseCourseState {
  refetch: () => Promise<void>;
}

export const useCourse = (id: number): UseCourseReturn => {
  const [state, setState] = useState<UseCourseState>({
    course: null,
    loading: true,
    error: null,
  });

  const fetchCourse = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const course = await courseService.getCourse(id);
      setState(prev => ({ ...prev, course, loading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch course';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
    }
  };

  useEffect(() => {
    if (id) {
      fetchCourse();
    }
  }, [id]);

  return {
    ...state,
    refetch: fetchCourse,
  };
};
