// src/hooks/useAuth.ts

import { useState, useEffect } from 'react';
import { type LoginRequest, type User } from '../types';
import { authService } from '../services/authService';

interface AuthState {
  token: string | null;
  isAuthenticated: boolean;
  user: User | null;
  expiresAt: Date | null;
}

export const useAuth = () => {
  const [state, setState] = useState<AuthState>({
    token: localStorage.getItem('token'),
    isAuthenticated: <PERSON><PERSON><PERSON>(localStorage.getItem('token')),
    user: localStorage.getItem('user')
      ? JSON.parse(localStorage.getItem('user')!)
      : null,
    expiresAt: localStorage.getItem('expiresAt')
      ? new Date(localStorage.getItem('expiresAt')!)
      : null,
  });

  // Check token expiration
  useEffect(() => {
    if (state.expiresAt && new Date() > state.expiresAt) {
      logout();
    }
  }, [state.expiresAt]);

  const login = async (credentials: LoginRequest): Promise<boolean> => {
    try {
      const { token, user, expires_at } = await authService.login(credentials);

      // Save to localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('expiresAt', expires_at);

      // Update state
      setState({
        token,
        isAuthenticated: true,
        user: user,
        expiresAt: new Date(expires_at),
      });

      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = () => {
    // Clear localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('expiresAt');

    // Update state
    setState({
      token: null,
      isAuthenticated: false,
      user: null,
      expiresAt: null,
    });
  };

  const getAuthHeader = () => {
    return state.token ? { Authorization: `Bearer ${state.token}` } : {};
  };

  return {
    state,
    login,
    logout,
    getAuthHeader,
  };
};
