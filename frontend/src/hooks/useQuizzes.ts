import { useState, useEffect } from 'react';
import { quizService } from '../services/quizService';
import { type Quiz } from '../types';

export const useQuizzes = () => {
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQuizzes = async () => {
    try {
      setLoading(true);
      setError(null);
      // This would need to be implemented to fetch all quizzes
      // For now, we'll return empty array
      setQuizzes([]);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch quizzes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuizzes();
  }, []);

  const refetch = () => {
    fetchQuizzes();
  };

  return {
    quizzes,
    loading,
    error,
    refetch,
  };
};

export const useQuizzesByCourse = (courseId: number) => {
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQuizzes = async () => {
    if (!courseId) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await quizService.getQuizzesByCourse(courseId);
      setQuizzes(data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch quizzes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuizzes();
  }, [courseId]);

  const refetch = () => {
    fetchQuizzes();
  };

  return {
    quizzes,
    loading,
    error,
    refetch,
  };
};

export const useQuiz = (quizId: number) => {
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQuiz = async () => {
    if (!quizId) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await quizService.getQuiz(quizId);
      setQuiz(data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch quiz');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuiz();
  }, [quizId]);

  const refetch = () => {
    fetchQuiz();
  };

  return {
    quiz,
    loading,
    error,
    refetch,
  };
};
