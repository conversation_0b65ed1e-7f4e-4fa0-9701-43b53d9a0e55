import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Row, Col, Al<PERSON>, <PERSON>, Badge } from 'react-bootstrap';
import { Plus, Trash2 } from 'lucide-react';
import {
  createBankQuestion,
  updateBankQuestion,
  createQuestionBankCategory,
  type CreateBankQuestionRequest,
  type UpdateBankQuestionRequest,
  type CreateBankOptionRequest
} from '../services/questionBankService';
import { type BankQuestion, type QuestionBankCategory, type DifficultyLevel } from '../types';

interface QuestionBankFormProps {
  show: boolean;
  onHide: () => void;
  question?: BankQuestion | null;
  categories: QuestionBankCategory[];
  onSuccess: () => void;
  onCategoriesReload?: () => void;
}

interface FormOption {
  option_text: string;
  is_correct: boolean;
  order_index: number;
}

const QuestionBankForm: React.FC<QuestionBankFormProps> = ({
  show,
  onHide,
  question,
  categories,
  onSuccess,
  onCategoriesReload
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showNewCategory, setShowNewCategory] = useState(false);

  // Form data
  const [categoryId, setCategoryId] = useState<number | ''>('');
  const [questionText, setQuestionText] = useState('');
  const [questionType, setQuestionType] = useState<'single_choice' | 'multiple_choice'>('single_choice');
  const [imageUrl, setImageUrl] = useState('');
  const [points, setPoints] = useState(1);
  const [difficulty, setDifficulty] = useState<DifficultyLevel>('normal');
  const [tags, setTags] = useState('');
  const [options, setOptions] = useState<FormOption[]>([
    { option_text: '', is_correct: false, order_index: 1 },
    { option_text: '', is_correct: false, order_index: 2 }
  ]);

  // New category form
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryDescription, setNewCategoryDescription] = useState('');

  useEffect(() => {
    if (show) {
      if (question) {
        // Edit mode
        setCategoryId(question.category_id);
        setQuestionText(question.question_text);
        setQuestionType(question.question_type);
        setImageUrl(question.image_url || '');
        setPoints(question.points);
        setDifficulty(question.difficulty);
        setTags(question.tags || '');

        if (question.options && question.options.length > 0) {
          setOptions(question.options.map(opt => ({
            option_text: opt.option_text,
            is_correct: opt.is_correct,
            order_index: opt.order_index
          })));
        }
      } else {
        // Create mode
        resetForm();
      }
    }
  }, [show, question]);

  const resetForm = () => {
    setCategoryId('');
    setQuestionText('');
    setQuestionType('single_choice');
    setImageUrl('');
    setPoints(1);
    setDifficulty('normal');
    setTags('');
    setOptions([
      { option_text: '', is_correct: false, order_index: 1 },
      { option_text: '', is_correct: false, order_index: 2 }
    ]);
    setError(null);
    setShowNewCategory(false);
    setNewCategoryName('');
    setNewCategoryDescription('');
  };

  const addOption = () => {
    setOptions([...options, {
      option_text: '',
      is_correct: false,
      order_index: options.length + 1
    }]);
  };

  const removeOption = (index: number) => {
    if (options.length > 2) {
      const newOptions = options.filter((_, i) => i !== index);
      // Reorder indices
      newOptions.forEach((opt, i) => {
        opt.order_index = i + 1;
      });
      setOptions(newOptions);
    }
  };

  const updateOption = (index: number, field: keyof FormOption, value: string | boolean) => {
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], [field]: value };

    // For single choice, ensure only one option is correct
    if (field === 'is_correct' && value === true && questionType === 'single_choice') {
      newOptions.forEach((opt, i) => {
        if (i !== index) {
          opt.is_correct = false;
        }
      });
    }

    setOptions(newOptions);
  };

  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) {
      setError('Tên danh mục không được để trống');
      return;
    }

    try {
      setLoading(true);
      const response = await createQuestionBankCategory({
        name: newCategoryName.trim(),
        description: newCategoryDescription.trim()
      });

      // Set the newly created category as selected
      setCategoryId(response.category.id);

      // Hide the new category form
      setShowNewCategory(false);
      setNewCategoryName('');
      setNewCategoryDescription('');

      // Call onCategoriesReload to reload categories without closing form
      if (onCategoriesReload) {
        onCategoriesReload();
      }

      setError(null); // Clear any previous errors
    } catch (err) {
      setError('Không thể tạo danh mục mới');
      console.error('Error creating category:', err);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): string | null => {
    if (!categoryId) return 'Vui lòng chọn danh mục';
    if (!questionText.trim()) return 'Nội dung câu hỏi không được để trống';
    if (points <= 0) return 'Điểm số phải lớn hơn 0';

    const validOptions = options.filter(opt => opt.option_text.trim());
    if (validOptions.length < 2) return 'Phải có ít nhất 2 lựa chọn';

    const correctOptions = validOptions.filter(opt => opt.is_correct);
    if (correctOptions.length === 0) return 'Phải có ít nhất 1 đáp án đúng';

    if (questionType === 'single_choice' && correctOptions.length > 1) {
      return 'Câu hỏi một đáp án chỉ được có 1 đáp án đúng';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const validOptions: CreateBankOptionRequest[] = options
        .filter(opt => opt.option_text.trim())
        .map((opt, index) => ({
          option_text: opt.option_text.trim(),
          is_correct: opt.is_correct,
          order_index: index + 1
        }));

      const requestData = {
        category_id: Number(categoryId),
        question_text: questionText.trim(),
        question_type: questionType,
        image_url: imageUrl.trim() || undefined,
        points,
        difficulty,
        tags: tags.trim(),
        options: validOptions
      };

      if (question) {
        // Update existing question
        await updateBankQuestion(question.id, requestData as UpdateBankQuestionRequest);
      } else {
        // Create new question
        await createBankQuestion(requestData as CreateBankQuestionRequest);
      }

      onSuccess();
      onHide();
    } catch (err) {
      setError('Không thể lưu câu hỏi');
      console.error('Error saving question:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {question ? 'Chỉnh sửa câu hỏi' : 'Thêm câu hỏi mới'}
        </Modal.Title>
      </Modal.Header>

      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <Row className="mb-3">
            <Col md={8}>
              <Form.Group>
                <Form.Label>Danh mục *</Form.Label>
                <Form.Select
                  value={categoryId}
                  onChange={(e) => setCategoryId(e.target.value as number | '')}
                  required
                >
                  <option value="">Chọn danh mục</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4} className="d-flex align-items-end">
              <Button
                variant="outline-primary"
                onClick={() => setShowNewCategory(!showNewCategory)}
                className="w-100"
              >
                <Plus size={16} className="me-2" />
                Danh mục mới
              </Button>
            </Col>
          </Row>

          {showNewCategory && (
            <Card className="mb-3">
              <Card.Body>
                <h6>Tạo danh mục mới</h6>
                <Row className="mb-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Tên danh mục</Form.Label>
                      <Form.Control
                        type="text"
                        value={newCategoryName}
                        onChange={(e) => setNewCategoryName(e.target.value)}
                        placeholder="Nhập tên danh mục"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Mô tả</Form.Label>
                      <Form.Control
                        type="text"
                        value={newCategoryDescription}
                        onChange={(e) => setNewCategoryDescription(e.target.value)}
                        placeholder="Mô tả danh mục"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Button
                  variant="success"
                  size="sm"
                  onClick={handleCreateCategory}
                  disabled={loading}
                >
                  Tạo danh mục
                </Button>
              </Card.Body>
            </Card>
          )}

          <Form.Group className="mb-3">
            <Form.Label>Nội dung câu hỏi *</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={questionText}
              onChange={(e) => setQuestionText(e.target.value)}
              placeholder="Nhập nội dung câu hỏi"
              required
            />
          </Form.Group>

          <Row className="mb-3">
            <Col md={4}>
              <Form.Group>
                <Form.Label>Loại câu hỏi</Form.Label>
                <Form.Select
                  value={questionType}
                  onChange={(e) => setQuestionType(e.target.value as 'single_choice' | 'multiple_choice')}
                >
                  <option value="single_choice">Một đáp án đúng</option>
                  <option value="multiple_choice">Nhiều đáp án đúng</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Độ khó</Form.Label>
                <Form.Select
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value as DifficultyLevel)}
                >
                  <option value="easy">Dễ</option>
                  <option value="normal">Trung bình</option>
                  <option value="hard">Khó</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Điểm số</Form.Label>
                <Form.Control
                  type="number"
                  min="0.1"
                  step="0.1"
                  value={points}
                  onChange={(e) => setPoints(Number(e.target.value))}
                />
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>URL hình ảnh (tùy chọn)</Form.Label>
                <Form.Control
                  type="url"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Tags (tùy chọn)</Form.Label>
                <Form.Control
                  type="text"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  placeholder="tag1, tag2, tag3"
                />
              </Form.Group>
            </Col>
          </Row>

          <div className="mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <Form.Label className="mb-0">Lựa chọn trả lời *</Form.Label>
              <Button variant="outline-primary" size="sm" onClick={addOption}>
                <Plus size={16} className="me-1" />
                Thêm lựa chọn
              </Button>
            </div>

            {questionType === 'single_choice' && (
              <div className="text-muted small mb-2">
                Chọn một đáp án đúng
              </div>
            )}

            {questionType === 'multiple_choice' && (
              <div className="text-muted small mb-2">
                Có thể chọn nhiều đáp án đúng
              </div>
            )}

            {options.map((option, index) => (
              <div key={index} className="d-flex align-items-center mb-2">
                <Form.Check
                  type={questionType === 'single_choice' ? 'radio' : 'checkbox'}
                  name="correct_answer"
                  checked={option.is_correct}
                  onChange={(e) => updateOption(index, 'is_correct', e.target.checked)}
                  className="me-2"
                />
                <Form.Control
                  type="text"
                  value={option.option_text}
                  onChange={(e) => updateOption(index, 'option_text', e.target.value)}
                  placeholder={`Lựa chọn ${index + 1}`}
                  className="me-2"
                />
                {options.length > 2 && (
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => removeOption(index)}
                  >
                    <Trash2 size={16} />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Hủy
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? 'Đang lưu...' : (question ? 'Cập nhật' : 'Tạo mới')}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default QuestionBankForm;
