/* Markdown Table Styles */
.slide-content table {
  width: 100%;
  margin: 1.5rem 0;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 0.95rem;
}

.slide-content table thead {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
}

.slide-content table th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  border: none;
}

.slide-content table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  vertical-align: middle;
}

.slide-content table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.slide-content table tr:last-child td {
  border-bottom: none;
}

.slide-content table tr:hover {
  background-color: rgba(99, 102, 241, 0.05);
}

/* Code blocks within tables */
.slide-content table code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85em;
  color: #e83e8c;
}

/* Responsive table */
@media (max-width: 768px) {
  .slide-content table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }
  
  .slide-content table th,
  .slide-content table td {
    padding: 0.5rem 0.75rem;
  }
}

/* Additional Markdown styling improvements */
.slide-content pre {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem 0;
  overflow-x: auto;
  border: 1px solid #e9ecef;
}

.slide-content code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  color: #e83e8c;
}

.slide-content blockquote {
  border-left: 4px solid var(--primary-color);
  padding-left: 1rem;
  margin-left: 0;
  color: #6c757d;
  font-style: italic;
}

.slide-content hr {
  border: 0;
  height: 1px;
  background: #e9ecef;
  margin: 2rem 0;
}

/* Improve list styling */
.slide-content ul, 
.slide-content ol {
  padding-left: 1.5rem;
}

.slide-content li {
  margin-bottom: 0.5rem;
}

.slide-content li > ul,
.slide-content li > ol {
  margin-top: 0.5rem;
}