import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import { ArrowLeft, CheckCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from "remark-gfm";
import { type Slide } from '../types';

interface SlideViewerProps {
  slides: Slide[];
  lessonTitle: string;
  onSlideChange?: (slideIndex: number, slide: Slide) => void;
  onSlideComplete?: (slideId: number) => void;
  onExit?: () => void;
  loading?: boolean;
  error?: string | null;
}

const SlideViewer: React.FC<SlideViewerProps> = ({
  slides,
  lessonTitle,
  onSlideChange,
  onSlideComplete,
  onExit,
  loading = false,
  error = null,
}) => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [completedSlides, setCompletedSlides] = useState<Set<number>>(new Set());

  // Navigation functions
  const goToNextSlide = () => {
    if (currentSlideIndex < slides.length - 1) {
      const newIndex = currentSlideIndex + 1;
      setCurrentSlideIndex(newIndex);

      // Mark current slide as complete and trigger callbacks
      const currentSlide = slides[currentSlideIndex];
      if (currentSlide && !completedSlides.has(currentSlide.id)) {
        setCompletedSlides(prev => new Set(prev).add(currentSlide.id));
        onSlideComplete?.(currentSlide.id);
      }

      // Trigger slide change callback
      if (slides[newIndex]) {
        onSlideChange?.(newIndex, slides[newIndex]);
      }
    }
  };

  const goToPreviousSlide = () => {
    if (currentSlideIndex > 0) {
      const newIndex = currentSlideIndex - 1;
      setCurrentSlideIndex(newIndex);

      // Trigger slide change callback
      if (slides[newIndex]) {
        onSlideChange?.(newIndex, slides[newIndex]);
      }
    }
  };

  // Mark current slide as viewed when component mounts or slide changes
  useEffect(() => {
    if (slides.length > 0 && slides[currentSlideIndex]) {
      const currentSlide = slides[currentSlideIndex];
      if (!completedSlides.has(currentSlide.id)) {
        setCompletedSlides(prev => new Set(prev).add(currentSlide.id));
        onSlideComplete?.(currentSlide.id);
      }
    }
  }, [currentSlideIndex, slides.length, completedSlides, onSlideComplete]);

  // Calculate progress based on highest slide reached, not just completed slides
  const getProgressPercentage = () => {
    if (slides.length === 0) return 0;

    // Find the highest slide index that has been viewed
    const viewedSlideIndices = slides
      .map((slide, index) => completedSlides.has(slide.id) ? index : -1)
      .filter(index => index !== -1);

    const highestViewedIndex = viewedSlideIndices.length > 0
      ? Math.max(...viewedSlideIndices, currentSlideIndex)
      : currentSlideIndex;

    // Progress is based on the furthest slide reached (1-based)
    return Math.round(((highestViewedIndex + 1) / slides.length) * 100);
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft' || event.key === 'ArrowUp') {
        event.preventDefault();
        goToPreviousSlide();
      } else if (event.key === 'ArrowRight' || event.key === 'ArrowDown' || event.key === ' ') {
        event.preventDefault();
        goToNextSlide();
      } else if (event.key === 'Escape') {
        event.preventDefault();
        onExit?.();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentSlideIndex, slides.length]);

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading slides...</span>
        </Spinner>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger">
        <strong>Error:</strong> {error}
      </Alert>
    );
  }

  if (slides.length === 0) {
    return (
      <Alert variant="info">
        <strong>No slides found</strong> for this lesson.
      </Alert>
    );
  }

  const currentSlide = slides[currentSlideIndex];

  return (
    <div className="slide-viewer">
      {/* Fixed Header - Lesson Title & Navigation */}
      <div className="slide-header">
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center">
            <Button variant="outline-secondary" onClick={onExit} className="me-3">
              <ArrowLeft size={16} className="me-1" />
              Back to Course
            </Button>
            <div>
              <h5 className="mb-0 lesson-title">{lessonTitle}</h5>
              <small className="text-muted">
                Slide {currentSlideIndex + 1} of {slides.length}
              </small>
            </div>
          </div>
          <div className="d-flex align-items-center">
            <Button
              variant="outline-primary"
              size="sm"
              onClick={goToPreviousSlide}
              disabled={currentSlideIndex === 0}
              className="me-2"
            >
              <ChevronLeft size={16} />
              <span className="d-none d-md-inline ms-1">Previous</span>
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={goToNextSlide}
              disabled={currentSlideIndex === slides.length - 1}
            >
              <span className="d-none d-md-inline me-1">Next</span>
              <ChevronRight size={16} />
            </Button>
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="slide-content-container">
        <div className="slide-content-wrapper">
          <Card className="slide-content-card">
            <Card.Body className="slide-card-body">
              {currentSlide.title && (
                <h2 className="slide-title text-center mb-4">{currentSlide.title}</h2>
              )}

              {currentSlide.image_url && (
                <div className="slide-image text-center mb-4">
                  <img
                    src={currentSlide.image_url}
                    alt={currentSlide.title || 'Slide image'}
                    className="img-fluid rounded shadow-sm"
                    style={{ maxHeight: '400px', objectFit: 'contain' }}
                  />
                </div>
              )}

              <div className="slide-content">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>{currentSlide.content}</ReactMarkdown>
              </div>

              {/* Slide completion indicator */}
              {completedSlides.has(currentSlide.id) && (
                <div className="text-center mt-4">
                  <div className="d-inline-flex align-items-center text-success">
                    <CheckCircle size={20} className="me-2" />
                    <span className="fw-medium">Slide Completed</span>
                  </div>
                </div>
              )}
            </Card.Body>
          </Card>
        </div>
      </div>

      {/* Fixed Footer - Progress */}
      <div className="slide-footer">
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center">
            <small className="text-muted me-3">
              Progress: {completedSlides.size} / {slides.length} slides viewed
            </small>
            <div className="progress" style={{ width: '200px', height: '6px' }}>
              <div
                className="progress-bar bg-success"
                role="progressbar"
                style={{ width: `${getProgressPercentage()}%` }}
                aria-valuenow={getProgressPercentage()}
                aria-valuemin={0}
                aria-valuemax={100}
              />
            </div>
          </div>
          <div className="d-flex align-items-center">
            <small className="text-muted me-3">
              {getProgressPercentage()}% Complete
            </small>
            {/* Keyboard shortcuts hint */}
            <small className="text-muted d-none d-lg-block">
              Use ← → arrow keys to navigate
            </small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SlideViewer;
