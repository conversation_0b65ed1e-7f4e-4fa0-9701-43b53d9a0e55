import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, Row, Col, Alert, Badge } from 'react-bootstrap';
import {
  createQuizRule,
  updateQuizRule,
  type CreateQuizRuleRequest,
  type UpdateQuizRuleRequest
} from '../services/quizRuleService';
import { getQuestionBankCategories } from '../services/questionBankService';
import { type QuizRule, type QuestionBankCategory, type DifficultyLevel } from '../types';

interface QuizRuleFormProps {
  show: boolean;
  onHide: () => void;
  quizId: number;
  rule?: QuizRule | null;
  onSuccess: () => void;
}

const QuizRuleForm: React.FC<QuizRuleFormProps> = ({
  show,
  onHide,
  quizId,
  rule,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<QuestionBankCategory[]>([]);

  // Form data
  const [categoryId, setCategoryId] = useState<number | ''>('');
  const [difficulty, setDifficulty] = useState<DifficultyLevel>('normal');
  const [questionType, setQuestionType] = useState<'single_choice' | 'multiple_choice'>('single_choice');
  const [percentage, setPercentage] = useState(10);
  const [orderIndex, setOrderIndex] = useState(1);

  useEffect(() => {
    if (show) {
      loadCategories();
      if (rule) {
        // Edit mode
        setCategoryId(rule.category_id);
        setDifficulty(rule.difficulty);
        setQuestionType(rule.question_type);
        setPercentage(rule.percentage);
        setOrderIndex(rule.order_index);
      } else {
        // Create mode
        resetForm();
      }
    }
  }, [show, rule]);

  const loadCategories = async () => {
    try {
      const response = await getQuestionBankCategories();
      setCategories(response.categories);
    } catch (err) {
      setError('Failed to load categories');
      console.error('Error loading categories:', err);
    }
  };

  const resetForm = () => {
    setCategoryId('');
    setDifficulty('normal');
    setQuestionType('single_choice');
    setPercentage(10);
    setOrderIndex(1);
    setError(null);
  };

  const validateForm = (): string | null => {
    if (!categoryId) return 'Vui lòng chọn danh mục';
    if (percentage <= 0 || percentage > 100) return 'Tỷ lệ phần trăm phải từ 0.1 đến 100';
    if (orderIndex <= 0) return 'Thứ tự phải lớn hơn 0';
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const requestData = {
        category_id: Number(categoryId),
        difficulty,
        question_type: questionType,
        percentage,
        order_index: orderIndex
      };

      if (rule) {
        // Update existing rule
        await updateQuizRule(rule.id, requestData as UpdateQuizRuleRequest);
      } else {
        // Create new rule
        await createQuizRule(quizId, requestData as CreateQuizRuleRequest);
      }

      onSuccess();
      onHide();
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Không thể lưu quy tắc';
      setError(errorMessage);
      console.error('Error saving rule:', err);
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyLabel = (diff: DifficultyLevel) => {
    switch (diff) {
      case 'easy': return 'Dễ';
      case 'normal': return 'Trung bình';
      case 'hard': return 'Khó';
      default: return diff;
    }
  };

  const getQuestionTypeLabel = (type: string) => {
    return type === 'single_choice' ? 'Một đáp án' : 'Nhiều đáp án';
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {rule ? 'Chỉnh sửa quy tắc' : 'Thêm quy tắc mới'}
        </Modal.Title>
      </Modal.Header>

      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Danh mục *</Form.Label>
                <Form.Select
                  value={categoryId}
                  onChange={(e) => setCategoryId(e.target.value as number | '')}
                  required
                >
                  <option value="">Chọn danh mục</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Thứ tự *</Form.Label>
                <Form.Control
                  type="number"
                  min="1"
                  value={orderIndex}
                  onChange={(e) => setOrderIndex(Number(e.target.value))}
                  required
                />
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={4}>
              <Form.Group>
                <Form.Label>Độ khó</Form.Label>
                <Form.Select
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value as DifficultyLevel)}
                >
                  <option value="easy">Dễ</option>
                  <option value="normal">Trung bình</option>
                  <option value="hard">Khó</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Loại câu hỏi</Form.Label>
                <Form.Select
                  value={questionType}
                  onChange={(e) => setQuestionType(e.target.value as 'single_choice' | 'multiple_choice')}
                >
                  <option value="single_choice">Một đáp án</option>
                  <option value="multiple_choice">Nhiều đáp án</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Tỷ lệ phần trăm *</Form.Label>
                <Form.Control
                  type="number"
                  min="0.1"
                  max="100"
                  step="0.1"
                  value={percentage}
                  onChange={(e) => setPercentage(Number(e.target.value))}
                  required
                />
                <Form.Text className="text-muted">
                  Tỷ lệ % của tổng số câu hỏi trong quiz
                </Form.Text>
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={12}>
              <div className="w-100">
                <h6>Xem trước quy tắc:</h6>
                <div className="p-3 bg-light rounded">
                  <div className="d-flex flex-wrap gap-2 mb-2">
                    <Badge bg="primary">{categories.find(c => c.id === Number(categoryId))?.name || 'Chưa chọn'}</Badge>
                    <Badge bg="warning">{getDifficultyLabel(difficulty)}</Badge>
                    <Badge bg="info">{getQuestionTypeLabel(questionType)}</Badge>
                  </div>
                  <div className="small">
                    <strong>Tỷ lệ:</strong> {percentage}% của tổng số câu hỏi
                    <br />
                    <strong>Thứ tự:</strong> {orderIndex}
                  </div>
                </div>
              </div>
            </Col>
          </Row>

          <Alert variant="info">
            <h6>Lưu ý:</h6>
            <ul className="mb-0">
              <li>Quy tắc sử dụng tỷ lệ phần trăm để tính số câu hỏi dựa trên tổng số câu hỏi của quiz</li>
              <li>Hệ thống sẽ chọn ngẫu nhiên câu hỏi phù hợp với điều kiện</li>
              <li>Đảm bảo ngân hàng câu hỏi có đủ câu hỏi theo điều kiện</li>
              <li>Ví dụ: Quiz có 10 câu, quy tắc 20% sẽ tạo 2 câu hỏi</li>
            </ul>
          </Alert>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Hủy
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? 'Đang lưu...' : (rule ? 'Cập nhật' : 'Tạo mới')}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default QuizRuleForm;
