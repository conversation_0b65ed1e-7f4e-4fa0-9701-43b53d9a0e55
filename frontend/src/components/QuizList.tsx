import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import type { Quiz } from '../types';

interface QuizListProps {
  quizzes: Quiz[];
  courseId: number;
}

const QuizList: React.FC<QuizListProps> = ({ quizzes, courseId }) => {
  const navigate = useNavigate();

  if (quizzes.length === 0) {
    return (
      <Card className="text-center p-4">
        <Card.Body>
          <p className="text-muted">No quizzes available for this course yet.</p>
        </Card.Body>
      </Card>
    );
  }

  return (
    <div className="quiz-list">
      <h5 className="mb-3">Available Quizzes</h5>
      {quizzes.map(quiz => (
        <Card key={quiz.id} className="mb-3 quiz-card">
          <Card.Body>
            <div className="d-flex justify-content-between align-items-start">
              <div>
                <h5>{quiz.title}</h5>
                <p className="text-muted small mb-2">{quiz.description}</p>
                <div className="quiz-meta">
                  <Badge bg="info" className="me-2">
                    {quiz.questions?.length || 0} questions
                  </Badge>
                  {quiz.time_limit > 0 && (
                    <Badge bg="secondary" className="me-2">
                      {quiz.time_limit} minutes
                    </Badge>
                  )}
                  <Badge bg="primary">
                    Pass score: {quiz.pass_score}%
                  </Badge>
                </div>
              </div>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => navigate(`/quizzes/${quiz.id}/attempt`)}
              >
                Start Quiz
              </Button>
            </div>
          </Card.Body>
        </Card>
      ))}
    </div>
  );
};

export default QuizList;