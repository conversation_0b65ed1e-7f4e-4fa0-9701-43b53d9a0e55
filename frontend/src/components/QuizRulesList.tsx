import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge, <PERSON><PERSON>, <PERSON><PERSON>, Table, Spinner } from 'react-bootstrap';
import { Plus, Edit, Trash2, Eye, AlertTriangle, CheckCircle } from 'lucide-react';
import {
  getQuizRules,
  deleteQuizRule,
  validateQuizRules,
  previewQuizQuestions,
  type ValidationResult,
  type PreviewResult
} from '../services/quizRuleService';
import { type QuizRule, calculateQuestionCount } from '../types';
import QuizRuleForm from './QuizRuleForm';

interface QuizRulesListProps {
  quizId: number;
  totalQuestions: number;
  onRulesUpdate?: () => void;
}

const QuizRulesList: React.FC<QuizRulesListProps> = ({ quizId, totalQuestions: totalQuestionsCount, onRulesUpdate }) => {
  const [rules, setRules] = useState<QuizRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingRule, setEditingRule] = useState<QuizRule | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingRule, setDeletingRule] = useState<QuizRule | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [validation, setValidation] = useState<ValidationResult[]>([]);
  const [preview, setPreview] = useState<PreviewResult[]>([]);
  const [isValid, setIsValid] = useState(false);
  const [totalQuestions, setTotalQuestions] = useState(0);

  useEffect(() => {
    loadRules();
  }, [quizId]);

  const loadRules = async () => {
    try {
      setLoading(true);
      const response = await getQuizRules(quizId);
      setRules(response.rules);

      // Auto-validate rules
      if (response.rules.length > 0) {
        await validateRules();
      }
    } catch (err) {
      setError('Failed to load quiz rules');
      console.error('Error loading rules:', err);
    } finally {
      setLoading(false);
    }
  };

  const validateRules = async () => {
    try {
      const response = await validateQuizRules(quizId);
      setValidation(response.rules_validation);
      setIsValid(response.valid);
      setTotalQuestions(response.total_questions);
    } catch (err) {
      console.error('Error validating rules:', err);
    }
  };

  const loadPreview = async () => {
    try {
      const response = await previewQuizQuestions(quizId);
      setPreview(response.preview);
      setShowPreview(true);
    } catch (err) {
      setError('Failed to load preview');
      console.error('Error loading preview:', err);
    }
  };

  const handleCreateRule = () => {
    setEditingRule(null);
    setShowForm(true);
  };

  const handleEditRule = (rule: QuizRule) => {
    setEditingRule(rule);
    setShowForm(true);
  };

  const handleDeleteRule = (rule: QuizRule) => {
    setDeletingRule(rule);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!deletingRule) return;

    try {
      await deleteQuizRule(deletingRule.id);
      setShowDeleteModal(false);
      setDeletingRule(null);
      loadRules();
      if (onRulesUpdate) {
        onRulesUpdate();
      }
    } catch (err) {
      setError('Failed to delete rule');
      console.error('Error deleting rule:', err);
    }
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingRule(null);
    loadRules();
    if (onRulesUpdate) {
      onRulesUpdate();
    }
  };

  const getDifficultyBadgeVariant = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'success';
      case 'normal': return 'warning';
      case 'hard': return 'danger';
      default: return 'secondary';
    }
  };

  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'Dễ';
      case 'normal': return 'TB';
      case 'hard': return 'Khó';
      default: return difficulty;
    }
  };

  const getQuestionTypeLabel = (type: string) => {
    return type === 'single_choice' ? 'Một đáp án' : 'Nhiều đáp án';
  };

  const getRuleValidation = (ruleId: number) => {
    return validation.find(v => v.rule_id === ruleId);
  };

  if (loading) {
    return (
      <div className="text-center py-4">
        <Spinner animation="border" />
        <div className="mt-2">Đang tải quy tắc...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h5>Quy tắc tạo câu hỏi</h5>
        <div className="d-flex gap-2">
          {rules.length > 0 && (
            <Button variant="outline-info" onClick={loadPreview}>
              <Eye size={16} className="me-2" />
              Xem trước
            </Button>
          )}
          <Button variant="primary" onClick={handleCreateRule}>
            <Plus size={16} className="me-2" />
            Thêm quy tắc
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Validation Summary */}
      {rules.length > 0 && validation.length > 0 && (
        <Alert variant={isValid ? 'success' : 'warning'} className="mb-3">
          <div className="d-flex align-items-center">
            {isValid ? (
              <CheckCircle size={20} className="me-2" />
            ) : (
              <AlertTriangle size={20} className="me-2" />
            )}
            <div>
              <strong>
                {isValid ? 'Quy tắc hợp lệ' : 'Có vấn đề với quy tắc'}
              </strong>
              <div className="small">
                Tổng số câu hỏi sẽ tạo: {totalQuestions} câu
              </div>
            </div>
          </div>
        </Alert>
      )}

      {rules.length === 0 ? (
        <Card>
          <Card.Body className="text-center py-5">
            <h6>Chưa có quy tắc nào</h6>
            <p className="text-muted">
              Thêm quy tắc để tự động tạo câu hỏi từ ngân hàng cho quiz này
            </p>
            <Button variant="primary" onClick={handleCreateRule}>
              <Plus size={20} className="me-2" />
              Thêm quy tắc đầu tiên
            </Button>
          </Card.Body>
        </Card>
      ) : (
        <div className="space-y-3">
          {rules.map((rule) => {
            const ruleValidation = getRuleValidation(rule.id);
            return (
              <Card key={rule.id} className="mb-3">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-start">
                    <div className="flex-grow-1">
                      <div className="d-flex align-items-center mb-2">
                        <Badge bg="secondary" className="me-2">#{rule.order_index}</Badge>
                        <Badge bg="primary" className="me-2">{rule.category?.name}</Badge>
                        <Badge bg={getDifficultyBadgeVariant(rule.difficulty)} className="me-2">
                          {getDifficultyLabel(rule.difficulty)}
                        </Badge>
                        <Badge bg="info" className="me-2">
                          {getQuestionTypeLabel(rule.question_type)}
                        </Badge>
                        <Badge bg="warning">
                          {rule.percentage.toFixed(1)}%
                        </Badge>
                        <Badge bg="dark" className="ms-2">
                          ≈{calculateQuestionCount(rule.percentage, totalQuestions)} câu
                        </Badge>
                      </div>

                      {ruleValidation && (
                        <div className="small">
                          {ruleValidation.valid ? (
                            <span className="text-success">
                              ✓ Có {ruleValidation.available_questions} câu hỏi khả dụng
                            </span>
                          ) : (
                            <span className="text-danger">
                              ⚠ {ruleValidation.error} (Có {ruleValidation.available_questions}, cần {ruleValidation.required_questions})
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="d-flex gap-2">
                      <Button
                        variant="outline-warning"
                        size="sm"
                        onClick={() => handleEditRule(rule)}
                      >
                        <Edit size={16} />
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => handleDeleteRule(rule)}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            );
          })}
        </div>
      )}

      {/* Rule Form Modal */}
      <QuizRuleForm
        show={showForm}
        onHide={() => setShowForm(false)}
        quizId={quizId}
        rule={editingRule}
        onSuccess={handleFormSuccess}
      />

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Xác nhận xóa</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Bạn có chắc chắn muốn xóa quy tắc này?</p>
          {deletingRule && (
            <div className="bg-light p-3 rounded">
              <strong>{deletingRule.category?.name}</strong> - {getDifficultyLabel(deletingRule.difficulty)} - {deletingRule.percentage}% (≈{calculateQuestionCount(deletingRule.percentage, totalQuestions)} câu)
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Hủy
          </Button>
          <Button variant="danger" onClick={confirmDelete}>
            Xóa
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Preview Modal */}
      <Modal show={showPreview} onHide={() => setShowPreview(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Xem trước câu hỏi</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {preview.map((item, index) => (
            <Card key={index} className="mb-3">
              <Card.Header>
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <Badge bg="primary" className="me-2">{item.rule.category?.name}</Badge>
                    <Badge bg={getDifficultyBadgeVariant(item.rule.difficulty)} className="me-2">
                      {getDifficultyLabel(item.rule.difficulty)}
                    </Badge>
                    <Badge bg="info">
                      {getQuestionTypeLabel(item.rule.question_type)}
                    </Badge>
                  </div>
                  <div className="text-muted small">
                    {item.available_questions} câu khả dụng / {calculateQuestionCount(item.rule.percentage, totalQuestions)} câu cần
                  </div>
                </div>
              </Card.Header>
              <Card.Body>
                {item.sample_questions.length > 0 ? (
                  <div>
                    <h6>Câu hỏi mẫu:</h6>
                    {item.sample_questions.map((question, qIndex) => (
                      <div key={qIndex} className="mb-2 p-2 bg-light rounded">
                        <div className="small text-muted">#{question.id}</div>
                        <div>{question.question_text}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-muted">Không có câu hỏi nào phù hợp</div>
                )}
              </Card.Body>
            </Card>
          ))}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPreview(false)}>
            Đóng
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default QuizRulesList;
