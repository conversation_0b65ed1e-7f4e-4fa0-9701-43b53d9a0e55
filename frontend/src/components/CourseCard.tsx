import React from 'react';
import { <PERSON>, <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { <PERSON><PERSON><PERSON>, Edit, Trash2, User, Clock } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';

interface CourseCardProps {
  course: any;
  onView?: (id: number) => void;
  onEdit?: (course: any) => void;
  onDelete?: (id: number) => void;
}

const CourseCard: React.FC<CourseCardProps> = ({ course, onView, onEdit, onDelete }) => {
  const { state } = useAuth();
  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';
  const isOwner = course.creator?.id === state.user?.id;
  const navigate = useNavigate();

  const handleCardClick = () => {
    if (onView) {
      onView(course.id);
    }
  };

  // Tạo placeholder image nếu không có thumbnail
  const thumbnailUrl = course.thumbnail || 'https://via.placeholder.com/300x150?text=No+Image';

  return (
    <Card className="h-100 shadow-sm" style={{ cursor: onView ? 'pointer' : 'default' }} onClick={handleCardClick}>
      <div style={{ position: 'relative' }}>
        <Card.Img 
          variant="top" 
          src={thumbnailUrl} 
          style={{ height: '150px', objectFit: 'cover' }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'https://via.placeholder.com/300x150?text=Image+Error';
          }}
        />
        {!course.is_published && (
          <Badge 
            bg="warning" 
            style={{ 
              position: 'absolute', 
              top: '10px', 
              right: '10px',
              fontSize: '0.7rem'
            }}
          >
            Chờ duyệt
          </Badge>
        )}
      </div>
      
      <Card.Body className="d-flex flex-column">
        <div className="d-flex justify-content-between align-items-start mb-2">
          <Card.Title 
            className="h5 mb-0" 
            onClick={handleCardClick}
            style={{ cursor: onView ? 'pointer' : 'default' }}
          >
            {course.title}
          </Card.Title>
          <div className="d-flex gap-1">
            <Badge bg={course.is_published ? 'success' : 'warning'}>
              {course.is_published ? 'Published' : 'Draft'}
            </Badge>
          </div>
        </div>

        <Card.Text className="text-muted small mb-2">
          {course.description || 'No description available'}
        </Card.Text>

        <div className="d-flex align-items-center text-muted small mb-2">
          <BookOpen size={14} className="me-1" />
          <span className="me-3">
            {course.lessons?.length || 0} lessons
          </span>
          <Clock size={14} className="me-1" />
          <span>Created {formatDate(course.created_at)}</span>
        </div>

        {course.creator && (
          <div className="text-muted small mb-3">
            <strong>Instructor:</strong> {course.creator.first_name} {course.creator.last_name}
          </div>
        )}

        <div className="mt-auto">
          <div className="d-flex gap-2">
            {onView && (
              <Button 
                variant="primary" 
                size="sm" 
                onClick={handleCardClick}
                className="flex-grow-1"
              >
                <BookOpen size={14} className="me-1" />
                View Course
              </Button>
            )}
            
            {isTeacherOrAdmin && (isOwner || state.user?.role === 'admin') && (
              <>
                {onEdit && (
                  <Button 
                    variant="outline-secondary" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/courses/${course.id}/edit`);
                    }}
                  >
                    <Edit size={14} />
                  </Button>
                )}
                
                {onDelete && (
                  <Button 
                    variant="outline-danger" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(course.id);
                    }}
                  >
                    <Trash2 size={14} />
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export default CourseCard;
