import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Home,
  BookOpen,
  Users,
  UserCog,
  FileText,
  BarChart3,
  Settings,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  Shield,
  GraduationCap,
  Database
} from 'lucide-react';

interface SidebarProps {
  isCollapsed: boolean;
  isMobileOpen: boolean;
  onToggle: () => void;
  onMobileToggle: () => void;
}

interface NavItem {
  to?: string;
  icon: any;
  label: string;
  children?: NavItem[];
  adminOnly?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  isMobileOpen,
  onToggle,
  onMobileToggle
}) => {
  // Get current user from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isAdmin = user.role === 'admin';
  const isTeacherOrAdmin = user.role === 'teacher' || user.role === 'admin';

  // Get current location
  const location = useLocation();

  // State for expanded sub-menus
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);

  // Helper function to check if parent menu should be active
  const isParentActive = (children: NavItem[]) => {
    return children.some(child => child.to === location.pathname);
  };

  // Auto-expand parent menu if child is active
  React.useEffect(() => {
    navItems.forEach(item => {
      if (item.children && isParentActive(item.children)) {
        setExpandedMenus(prev =>
          prev.includes(item.label) ? prev : [...prev, item.label]
        );
      }
    });
  }, [location.pathname]);

  const toggleSubMenu = (label: string) => {
    setExpandedMenus(prev =>
      prev.includes(label)
        ? prev.filter(item => item !== label)
        : [...prev, label]
    );
  };

  const navItems: NavItem[] = [
    { to: '/dashboard', icon: Home, label: 'Dashboard' },
    { to: '/courses', icon: BookOpen, label: 'Courses' },
    {
      icon: GraduationCap,
      label: 'Learning',
      children: [
        { to: '/students', icon: Users, label: 'Students' },
        { to: '/quizzes', icon: FileText, label: 'Quizzes' },
        { to: '/question-bank', icon: Database, label: 'Question Bank' },
      ]
    },
    ...(isAdmin ? [{
      icon: Shield,
      label: 'Administration',
      children: [
        { to: '/users', icon: UserCog, label: 'User Management' },
        { to: '/analytics', icon: BarChart3, label: 'Analytics' },
      ]
    }] : []),
    { to: '/settings', icon: Settings, label: 'Settings' },
  ];

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="d-md-none position-fixed w-100 h-100"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 999,
            top: 0,
            left: 0
          }}
          onClick={onMobileToggle}
        />
      )}

      {/* Sidebar */}
      <div className={`sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobileOpen ? 'mobile-open' : ''}`}>
        {/* Sidebar Header */}
        <div className="sidebar-header">
          <div className="sidebar-logo" style={{ position: 'relative' }}>
            {isCollapsed ? (
              <>
                <img
                  src="/logo.png"
                  alt="Etuto"
                  className="sidebar-logo-img"
                  style={{ height: '32px', width: 'auto' }}
                />
                <div className="nav-tooltip-container">
                  <div className="nav-tooltip">
                    Etuto Admin
                  </div>
                </div>
              </>
            ) : (
              <img
                src="/logo_with_name.png"
                alt="Etuto Admin"
                className="sidebar-logo-img"
                style={{ height: '32px', width: 'auto' }}
              />
            )}
          </div>
          {/* Mobile close button only */}
          <button
            className="sidebar-toggle d-md-none"
            onClick={onMobileToggle}
            aria-label="Close sidebar"
          >
            <X size={20} />
          </button>
        </div>

        {/* Navigation */}
        <nav className="sidebar-nav">
          {navItems.map((item) => {
            const IconComponent = item.icon;

            // If item has children (sub-menu)
            if (item.children) {
              const isExpanded = expandedMenus.includes(item.label);
              const parentActive = isParentActive(item.children);

              return (
                <div key={item.label} className="nav-item nav-item-parent">
                  <div
                    className={`nav-link nav-link-parent ${parentActive ? 'active' : ''}`}
                    onClick={() => {
                      if (!isCollapsed) {
                        toggleSubMenu(item.label);
                      }
                    }}
                  >
                    <IconComponent className="nav-icon" size={20} />
                    <span className="nav-text">{item.label}</span>
                    {!isCollapsed && (
                      <span className="nav-arrow">
                        {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                      </span>
                    )}
                  </div>

                  {/* Tooltip for collapsed sidebar */}
                  {isCollapsed && (
                    <div className="nav-tooltip-container">
                      <div className="nav-tooltip nav-tooltip-submenu">
                        <div className="tooltip-title">{item.label}</div>
                        <div className="tooltip-submenu">
                          {item.children.map((subItem) => (
                            <NavLink
                              key={subItem.to}
                              to={subItem.to!}
                              className="tooltip-submenu-item"
                              onClick={() => {
                                // Close mobile sidebar when clicking a link
                                if (window.innerWidth < 768) {
                                  onMobileToggle();
                                }
                              }}
                            >
                              <subItem.icon size={14} />
                              <span>{subItem.label}</span>
                            </NavLink>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Sub-menu items */}
                  {!isCollapsed && isExpanded && (
                    <div className="nav-submenu">
                      {item.children.map((subItem) => {
                        const SubIconComponent = subItem.icon;
                        return (
                          <div key={subItem.to} className="nav-item nav-item-sub">
                            <NavLink
                              to={subItem.to!}
                              className={({ isActive }) =>
                                `nav-link nav-link-sub ${isActive ? 'active' : ''}`
                              }
                              onClick={() => {
                                // Close mobile sidebar when clicking a link
                                if (window.innerWidth < 768) {
                                  onMobileToggle();
                                }
                              }}
                            >
                              <SubIconComponent className="nav-icon" size={18} />
                              <span className="nav-text">{subItem.label}</span>
                            </NavLink>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            }

            // Regular menu item (no children)
            return (
              <div key={item.to} className="nav-item">
                <NavLink
                  to={item.to!}
                  className={({ isActive }) =>
                    `nav-link ${isActive ? 'active' : ''}`
                  }
                  onClick={() => {
                    // Close mobile sidebar when clicking a link
                    if (window.innerWidth < 768) {
                      onMobileToggle();
                    }
                  }}
                >
                  <IconComponent className="nav-icon" size={20} />
                  <span className="nav-text">{item.label}</span>
                </NavLink>
                {/* Tooltip for collapsed sidebar */}
                {isCollapsed && (
                  <div className="nav-tooltip-container">
                    <div className="nav-tooltip">
                      {item.label}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </nav>


      </div>
    </>
  );
};

export default Sidebar;
