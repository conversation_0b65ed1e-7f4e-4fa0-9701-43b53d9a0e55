import React from 'react';
import { <PERSON>u, Bell, User, LogOut } from 'lucide-react';
import { Dropdown } from 'react-bootstrap';
import { useAuth } from '../hooks/useAuth';

interface HeaderProps {
  onMobileMenuToggle: () => void;
  onSidebarToggle: () => void;
  sidebarCollapsed: boolean;
}

const Header: React.FC<HeaderProps> = ({ onMobileMenuToggle, onSidebarToggle, sidebarCollapsed }) => {
  const { state, logout } = useAuth();

  const handleLogout = () => {
    logout();

    // Redirect to login page
    window.location.href = '/login';
  };

  return (
    <header className="main-header">
      <div className="d-flex align-items-center">
        <button
          className="btn btn-link d-md-none p-2 me-2"
          onClick={onMobileMenuToggle}
          aria-label="Toggle mobile menu"
          style={{ color: '#6b7280' }}
        >
          <Menu size={20} />
        </button>
        <button
          className="btn btn-link d-none d-md-flex p-2"
          onClick={onSidebarToggle}
          aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          style={{ color: '#6b7280' }}
        >
          <Menu size={20} />
        </button>
      </div>

      <div className="header-actions">
        {/* Notifications */}
        <button
          className="btn btn-link p-2"
          style={{ color: '#6b7280' }}
          aria-label="Notifications"
        >
          <Bell size={20} />
        </button>

        {/* User Menu */}
        <Dropdown align="end">
          <Dropdown.Toggle
            variant="link"
            className="btn btn-link p-2 d-flex align-items-center"
            style={{
              color: '#6b7280',
              textDecoration: 'none',
              border: 'none'
            }}
          >
            <User size={20} className="me-2" />
            <span className="d-none d-sm-inline">
              {state?.user?.first_name}
            </span>
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Header>
              <div className="fw-bold">{state?.user?.first_name} {state?.user?.last_name}</div>
              <div className="small">{state?.user?.email}</div>
            </Dropdown.Header>
            <Dropdown.Divider />
            <Dropdown.Item href="#/profile">
              <User size={16} className="me-2" />
              Profile
            </Dropdown.Item>
            <Dropdown.Item href="#/settings">
              <User size={16} className="me-2" />
              Settings
            </Dropdown.Item>
            <Dropdown.Divider />
            <Dropdown.Item onClick={handleLogout}>
              <LogOut size={16} className="me-2" />
              Logout
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </div>
    </header>
  );
};

export default Header;
