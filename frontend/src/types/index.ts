export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'teacher' | 'student';
  avatar?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Course {
  id: number;
  title: string;
  description: string;
  thumbnail?: string;
  creator_id: number;
  is_published: boolean;
  created_at: string;
  updated_at: string;
  creator?: User;
  lessons?: Lesson[];
  quizzes?: Quiz[];
  enrollments?: Enrollment[];
}

export interface Lesson {
  id: number;
  course_id: number;
  title: string;
  description: string;
  order_index: number;
  created_at: string;
  updated_at: string;
  course?: Course;
  slides?: Slide[];
}

export interface Slide {
  id: number;
  lesson_id: number;
  title?: string;
  content: string;
  image_url?: string;
  order_index: number;
  created_at: string;
  updated_at: string;
  lesson?: Lesson;
}

export type DifficultyLevel = 'easy' | 'normal' | 'hard';

export interface Category {
  id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  quizzes?: Quiz[];
}

export interface Quiz {
  id: number;
  course_id: number;
  title: string;
  description: string;
  time_limit: number;
  pass_score: number;
  is_published: boolean;
  difficulty: DifficultyLevel;
  category_id?: number;
  total_questions: number;
  created_at: string;
  updated_at: string;
  course?: Course;
  category?: Category;
  questions?: Question[];
  quiz_attempts?: QuizAttempt[];
}

export interface Question {
  id: number;
  quiz_id: number;
  question_text: string;
  question_type: 'single_choice' | 'multiple_choice';
  image_url?: string;
  points: number;
  order_index: number;
  created_at: string;
  updated_at: string;
  quiz?: Quiz;
  options?: Option[];
}

export interface Option {
  id: number;
  question_id: number;
  option_text: string;
  is_correct: boolean;
  order_index: number;
  created_at: string;
  updated_at: string;
  question?: Question;
}

export interface QuizAttempt {
  id: number;
  user_id: number;
  quiz_id: number;
  score: number;
  max_score: number;
  percentage: number;
  is_passed: boolean;
  started_at: string;
  completed_at: string;
  created_at: string;
  updated_at: string;
  user?: User;
  quiz?: Quiz;
  answers?: Answer[];
}

export interface Answer {
  id: number;
  quiz_attempt_id: number;
  question_id: number;
  option_id: number;
  quiz_attempt?: QuizAttempt;
  question?: Question;
  option?: Option;
}

export interface Enrollment {
  id: number;
  user_id: number;
  course_id: number;
  enrolled_at: string;
  progress: number;
  user?: User;
  course?: Course;
}

export interface UserProgress {
  id: number;
  user_id: number;
  course_id: number;
  lesson_id: number;
  slide_id: number;
  is_completed: boolean;
  completed_at: string;
  created_at: string;
  updated_at: string;
  user?: User;
  course?: Course;
  lesson?: Lesson;
  slide?: Slide;
}

export interface AuthResponse {
  token: string;
  user: User;
  expires_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

export interface ApiResponse<T> {
  [key: string]: T;
}

export interface DashboardStats {
  totalCourses: number;
  totalStudents: number;
  totalEnrollments: number;
  totalQuizzes: number;
}

// Question Bank Types
export interface QuestionBankCategory {
  id: number;
  name: string;
  description: string;
  course_id?: number;
  created_at: string;
  updated_at: string;
  course?: Course;
  bank_questions?: BankQuestion[];
}

export interface BankQuestion {
  id: number;
  category_id: number;
  question_text: string;
  question_type: 'single_choice' | 'multiple_choice';
  image_url?: string;
  points: number;
  difficulty: DifficultyLevel;
  tags: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  category?: QuestionBankCategory;
  creator?: User;
  options?: BankOption[];
  quiz_attempt_questions?: QuizAttemptQuestion[];
}

export interface BankOption {
  id: number;
  bank_question_id: number;
  option_text: string;
  is_correct: boolean;
  order_index: number;
  created_at: string;
  updated_at: string;
  bank_question?: BankQuestion;
}

export interface QuizRule {
  id: number;
  quiz_id: number;
  category_id: number;
  difficulty: DifficultyLevel;
  question_type: 'single_choice' | 'multiple_choice';
  percentage: number;
  order_index: number;
  created_at: string;
  updated_at: string;
  quiz?: Quiz;
  category?: QuestionBankCategory;
}

// Helper function to calculate count from percentage
export const calculateQuestionCount = (percentage: number, totalQuestions: number): number => {
  const count = Math.floor(totalQuestions * percentage / 100);
  return count < 1 && percentage > 0 ? 1 : count;
};

export interface QuizAttemptQuestion {
  id: number;
  quiz_attempt_id: number;
  bank_question_id: number;
  order_index: number;
  points: number;
  created_at: string;
  updated_at: string;
  quiz_attempt?: QuizAttempt;
  bank_question?: BankQuestion;
}
