:root {
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  --header-height: 60px;
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --sidebar-bg: #1f2937;
  --sidebar-text: #d1d5db;
  --sidebar-hover: #374151;
  --border-color: #e5e7eb;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

.app {
  min-height: 100vh;
}

/* Login Page Styles */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.login-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  width: var(--sidebar-width);
  transition: width 0.3s ease;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--header-height);
  position: relative;
}

.sidebar-logo {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-logo-img {
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-logo {
  opacity: 1;
}

.sidebar.collapsed .sidebar-logo-img {
  opacity: 1;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--sidebar-text);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.sidebar-toggle:hover {
  background-color: var(--sidebar-hover);
}

/* Mobile close button positioning */
.sidebar-header .sidebar-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

.sidebar-nav {
  padding: 1rem 0;
  flex: 1;
  overflow-y: auto;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--sidebar-text);
  text-decoration: none;
  transition: all 0.2s;
  border-radius: 0;
  margin: 0 0.5rem;
  border-radius: 0.375rem;
}

.nav-link:hover {
  background-color: var(--sidebar-hover);
  color: white;
}

.nav-link.active {
  background-color: var(--primary-color);
  color: white;
}

/* Parent menu active state when child is active */
.nav-link-parent.active {
  background-color: var(--primary-color);
  color: white;
}

.nav-icon {
  margin-right: 0.75rem;
  min-width: 20px;
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
  display: none;
}

.sidebar.collapsed .nav-icon {
  margin-right: 0;
}

/* Hover tooltip for collapsed sidebar */
.sidebar.collapsed .nav-item {
  position: relative;
}

.sidebar.collapsed .nav-tooltip-container {
  position: absolute;
  left: 100%;
  top: 0;
  height: 100%;
  width: 12px;
  /* Small bridge area to make it easier to hover */
  overflow: visible;
  z-index: 1001;
  pointer-events: none;
}

.sidebar.collapsed .nav-tooltip {
  position: fixed;
  left: 80px;
  background-color: #1f2937;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  z-index: 1001;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #374151;
  pointer-events: auto;
}



.sidebar.collapsed .nav-tooltip::before {
  content: '';
  position: absolute;
  left: -5px;
  top: 50%;
  transform: translateY(-50%);
  border: 5px solid transparent;
  border-right-color: #1f2937;
}

.sidebar.collapsed .nav-item:hover .nav-tooltip,
.sidebar.collapsed .nav-tooltip:hover,
.sidebar.collapsed .nav-tooltip-container:hover .nav-tooltip {
  opacity: 1;
  visibility: visible;
  transition-delay: 0s;
}

.sidebar.collapsed .nav-tooltip {
  transition: opacity 0.2s ease 0.3s, visibility 0.2s ease 0.3s;
}



.sidebar.collapsed .nav-link {
  justify-content: center;
  position: relative;
}

/* Logo hover tooltip */
.sidebar.collapsed .sidebar-logo {
  position: relative;
}

.sidebar.collapsed .sidebar-logo .nav-tooltip-container {
  position: absolute;
  left: 100%;
  top: 0;
  height: 100%;
  width: 0;
  overflow: visible;
  z-index: 1001;
  pointer-events: none;
}

.sidebar.collapsed .sidebar-logo .nav-tooltip {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background-color: #1f2937;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #374151;
  pointer-events: none;
}

.sidebar.collapsed .sidebar-logo:hover .nav-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Sub-menu styles */
.nav-item-parent .nav-link-parent {
  cursor: pointer;
  position: relative;
}

.nav-arrow {
  margin-left: auto;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
}

.nav-submenu {
  background-color: rgba(0, 0, 0, 0.1);
  border-left: 2px solid #3b82f6;
  margin-left: 1rem;
  padding-left: 0;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }

  to {
    max-height: 200px;
    opacity: 1;
  }
}

.nav-item-sub {
  margin: 0;
}

.nav-link-sub {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  border-radius: 0;
}

.nav-link-sub:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.nav-link-sub.active {
  background-color: #3b82f6;
  color: white;
  border-left: 3px solid #1d4ed8;
}

.nav-link-sub .nav-icon {
  width: 18px;
  height: 18px;
  margin-right: 0.75rem;
}

/* Collapsed sidebar sub-menu behavior */
.sidebar.collapsed .nav-submenu {
  display: none;
}

.sidebar.collapsed .nav-arrow {
  display: none;
}

.sidebar.collapsed .nav-link-parent {
  justify-content: center;
}

/* Enhanced tooltips for sub-menus */
.nav-tooltip-submenu {
  min-width: 200px;
  padding: 0;
}

.tooltip-title {
  padding: 0.5rem 0.75rem;
  font-weight: 600;
  border-bottom: 1px solid #374151;
  background-color: #111827;
}

.tooltip-submenu {
  padding: 0.25rem 0;
}

.tooltip-submenu-item {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
  color: #d1d5db;
  text-decoration: none;
  transition: background-color 0.15s ease;
  border-radius: 0;
}

.tooltip-submenu-item:hover {
  background-color: #374151;
  color: #f3f4f6;
  text-decoration: none;
}

.tooltip-submenu-item.active {
  background-color: #3b82f6;
  color: white;
}

.tooltip-submenu-item svg {
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.tooltip-submenu-item span {
  white-space: nowrap;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #374151;
  margin-top: auto;
}

.sidebar-toggle-bottom {
  background: none;
  border: none;
  color: var(--sidebar-text);
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 0.875rem;
}

.sidebar-toggle-bottom:hover {
  background-color: var(--sidebar-hover);
  color: white;
}

.sidebar.collapsed .sidebar-toggle-bottom {
  justify-content: center;
}

.sidebar.collapsed .toggle-text {
  display: none;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

.main-content.sidebar-collapsed {
  margin-left: var(--sidebar-collapsed-width);
}

/* Header */
.main-header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  padding: 0 1.5rem;
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Content Area */
.content-area {
  padding: 1rem;
}

/* Cards */
.dashboard-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .main-content.sidebar-collapsed {
    margin-left: 0;
  }
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.error-message {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Course Card Styles */
.course-card {
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid #e5e7eb;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Spinning animation for refresh button */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* User Management Styles */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.user-table .table td {
  vertical-align: middle;
  padding: 1rem 0.75rem;
}

.user-table .table th {
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
  color: #495057;
  padding: 1rem 0.75rem;
}

.user-stats-card {
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid #e9ecef;
}

.user-stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.role-badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
}

.status-badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
}

/* Slide Viewer Styles */
.lesson-viewer-page {
  background-color: #f8f9fa;
  min-height: calc(100vh - var(--header-height));
}

.slide-viewer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 1rem;
  display: flex;
  flex-direction: column;
  height: calc(100vh - var(--header-height) - 2rem);
  overflow: hidden;
}

/* Fixed Header */
.slide-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem 1.5rem;
  flex-shrink: 0;
  z-index: 10;
  border-radius: 8px 8px 0 0;
}

.lesson-title {
  color: var(--primary-color);
  font-weight: 600;
}

/* Scrollable Content Container */
.slide-content-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  background-color: #f8f9fa;
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
.slide-content-container::-webkit-scrollbar {
  width: 8px;
}

.slide-content-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.slide-content-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.slide-content-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.slide-content-wrapper {
  padding: 1rem;
  min-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-content-card {
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.slide-card-body {
  padding: 2rem 1.5rem;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.slide-title {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.slide-content {
  font-size: 1.1rem;
  line-height: 1.8;
  max-width: 100%;
  text-align: left;
  margin: 0 auto;
}

.slide-content p {
  margin-bottom: 1.5rem;
}

.slide-content strong {
  color: var(--primary-color);
  font-weight: 600;
}

.slide-content em {
  font-style: italic;
  color: #6c757d;
}

.slide-content .code-snippet {
  background-color: #f8f9fa;
  padding: 0.3rem 0.6rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  color: #e83e8c;
  border: 1px solid #e9ecef;
}

.slide-content ul,
.slide-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.slide-content li {
  margin-bottom: 0.5rem;
}

.slide-image {
  max-width: 100%;
  display: flex;
  justify-content: center;
}

/* Fixed Footer */
.slide-footer {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 1rem 1.5rem;
  flex-shrink: 0;
  z-index: 10;
  border-radius: 0 0 8px 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .slide-viewer {
    margin: 0.5rem;
    height: calc(100vh - var(--header-height) - 1rem);
  }

  .slide-header {
    padding: 0.75rem 1rem;
  }

  .slide-footer {
    padding: 0.75rem 1rem;
  }

  .slide-content-wrapper {
    padding: 0.5rem;
  }

  .slide-card-body {
    padding: 1.5rem 1rem;
  }

  .slide-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .slide-content {
    font-size: 1rem;
  }
}

/* Custom slide transitions */
.slide-content-card {
  transition: all 0.3s ease-in-out;
}

.slide-content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}