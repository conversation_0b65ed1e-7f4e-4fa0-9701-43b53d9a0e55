import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';
import { UserPlus, Users } from 'lucide-react';

const Students: React.FC = () => {
  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>Students</h2>
        <Button variant="primary">
          <UserPlus size={16} className="me-2" />
          Add Student
        </Button>
      </div>

      <Row>
        <Col lg={8}>
          <Card>
            <Card.Body className="text-center py-5">
              <Users size={64} className="text-muted mb-3" />
              <h4>Student Management</h4>
              <p className="text-muted">
                This page will contain student management functionality including:
              </p>
              <ul className="list-unstyled text-muted">
                <li>• View all registered students</li>
                <li>• Manage student profiles</li>
                <li>• Track learning progress</li>
                <li>• Send notifications</li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={4}>
          <Card>
            <Card.Header>
              <h6 className="mb-0">Student Stats</h6>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Active Students</span>
                  <strong>156</strong>
                </div>
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>New This Month</span>
                  <strong>23</strong>
                </div>
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Completion Rate</span>
                  <strong>78%</strong>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Students;
