import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Button, Row, Col, Spinner, Alert, Form } from 'react-bootstrap';
import { Plus, BookOpen, RefreshCw } from 'lucide-react';
import { useCourses } from '../hooks/useCourses';
import { useAuth } from '../hooks/useAuth';
import CourseCard from '../components/CourseCard';
import { courseService } from '../services/courseService';

const Courses: React.FC = () => {
  const navigate = useNavigate();
  const { courses, loading, error, refetch } = useCourses();
  const { state } = useAuth();
  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';

  const handleViewCourse = (courseId: number) => {
    navigate(`/courses/${courseId}`);
  };

  const handleEditCourse = (course: any) => {
    navigate(`/courses/${course.id}/edit`);
  };

  const handleDeleteCourse = (courseId: number) => {
    // TODO: Implement delete course with confirmation
    console.log('Delete course:', courseId);
  };

  const handleAddCourse = () => {
    // TODO: Open add course modal/page
    console.log('Add new course');
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>Courses</h2>
        <div className="d-flex gap-2">
          <Button
            variant="outline-secondary"
            onClick={refetch}
            disabled={loading}
          >
            <RefreshCw size={16} className={`me-2 ${loading ? 'spin' : ''}`} />
            Refresh
          </Button>
          {isTeacherOrAdmin && (
            <Button variant="primary" onClick={handleAddCourse}>
              <Plus size={16} className="me-2" />
              Add Course
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <strong>Error:</strong> {error}
          <Button
            variant="link"
            className="p-0 ms-2"
            onClick={refetch}
          >
            Try again
          </Button>
        </Alert>
      )}

      {loading ? (
        <div className="text-center py-5">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="text-muted">Loading courses...</p>
        </div>
      ) : courses.length === 0 ? (
        <Row>
          <Col lg={8}>
            <Card>
              <Card.Body className="text-center py-5">
                <BookOpen size={64} className="text-muted mb-3" />
                <h4>No Courses Found</h4>
                <p className="text-muted">
                  {isTeacherOrAdmin
                    ? "Get started by creating your first course!"
                    : "No courses are available at the moment."
                  }
                </p>
                {isTeacherOrAdmin && (
                  <Button variant="primary" onClick={handleAddCourse}>
                    <Plus size={16} className="me-2" />
                    Create Your First Course
                  </Button>
                )}
              </Card.Body>
            </Card>
          </Col>
          <Col lg={4}>
            <Card>
              <Card.Header>
                <h6 className="mb-0">Quick Actions</h6>
              </Card.Header>
              <Card.Body>
                <div className="d-grid gap-2">
                  {isTeacherOrAdmin && (
                    <>
                      <Button variant="outline-primary" size="sm">
                        Import Course
                      </Button>
                      <Button variant="outline-info" size="sm">
                        Course Templates
                      </Button>
                    </>
                  )}
                  <Button variant="outline-secondary" size="sm">
                    Export Data
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      ) : (
        <Row>
          {courses.map((course) => (
            <Col key={course.id} lg={4} md={6} className="mb-4">
              <CourseCard
                course={course}
                onView={handleViewCourse}
                onEdit={isTeacherOrAdmin ? handleEditCourse : undefined}
                onDelete={isTeacherOrAdmin ? handleDeleteCourse : undefined}
              />
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default Courses;
