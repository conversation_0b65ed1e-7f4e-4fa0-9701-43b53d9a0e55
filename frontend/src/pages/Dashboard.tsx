import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { BookOpen, Users, FileText, TrendingUp } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { dashboardService } from '../services/dashboardService';
import { type DashboardStats, type Course, type User } from '../types';

const Dashboard: React.FC = () => {
  const { state } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalCourses: 0,
    totalStudents: 0,
    totalEnrollments: 0,
    totalQuizzes: 0,
  });
  const [recentCourses, setRecentCourses] = useState<Course[]>([]);
  const [recentUsers, setRecentUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const [statsData, coursesData, usersData] = await Promise.all([
          dashboardService.getStats(),
          dashboardService.getRecentCourses(),
          dashboardService.getRecentUsers(),
        ]);

        setStats(statsData);
        setRecentCourses(coursesData);
        setRecentUsers(usersData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const statCards = [
    {
      title: 'Total Courses',
      value: stats.totalCourses,
      icon: BookOpen,
      color: '#6366f1',
      bgColor: '#f0f9ff',
    },
    {
      title: 'Total Students',
      value: stats.totalStudents,
      icon: Users,
      color: '#10b981',
      bgColor: '#f0fdf4',
    },
    {
      title: 'Total Enrollments',
      value: stats.totalEnrollments,
      icon: TrendingUp,
      color: '#f59e0b',
      bgColor: '#fffbeb',
    },
    {
      title: 'Total Quizzes',
      value: stats.totalQuizzes,
      icon: FileText,
      color: '#ef4444',
      bgColor: '#fef2f2',
    },
  ];

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Welcome Section */}
      <div className="dashboard-card mb-4">
        <h2 className="mb-2">Welcome back, {state?.user?.email}!</h2>
        <p className="text-muted mb-0">
          Here's what's happening with your learning platform today.
        </p>
      </div>

      {/* Stats Cards */}
      <Row className="mb-4">
        {statCards.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <Col key={index} xs={12} sm={6} lg={3} className="mb-3">
              <div className="stat-card">
                <div
                  className="d-inline-flex align-items-center justify-content-center rounded-circle mb-3"
                  style={{
                    width: '60px',
                    height: '60px',
                    backgroundColor: stat.bgColor,
                    color: stat.color,
                  }}
                >
                  <IconComponent size={24} />
                </div>
                <div className="stat-number">{stat.value}</div>
                <div className="stat-label">{stat.title}</div>
              </div>
            </Col>
          );
        })}
      </Row>

      {/* Recent Activity */}
      <Row>
        <Col lg={8} className="mb-4">
          <Card>
            <Card.Header>
              <h5 className="mb-0">Recent Courses</h5>
            </Card.Header>
            <Card.Body>
              {recentCourses.length > 0 ? (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Course Title</th>
                      <th>Status</th>
                      <th>Created</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentCourses.map((course) => (
                      <tr key={course.id}>
                        <td>
                          <div>
                            <div className="fw-medium">{course.title}</div>
                            <small className="text-muted">
                              {course.description?.substring(0, 60)}...
                            </small>
                          </div>
                        </td>
                        <td>
                          <Badge
                            bg={course.is_published ? 'success' : 'warning'}
                            text={course.is_published ? 'light' : 'dark'}
                          >
                            {course.is_published ? 'Published' : 'Draft'}
                          </Badge>
                        </td>
                        <td>
                          <small className="text-muted">
                            {new Date(course.created_at).toLocaleDateString()}
                          </small>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <BookOpen size={48} className="text-muted mb-3" />
                  <p className="text-muted">No courses found</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4} className="mb-4">
          <Card>
            <Card.Header>
              <h5 className="mb-0">Recent Users</h5>
            </Card.Header>
            <Card.Body>
              {recentUsers.length > 0 ? (
                <div>
                  {recentUsers.map((user) => (
                    <div key={user.id} className="d-flex align-items-center mb-3">
                      <div
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{
                          width: '40px',
                          height: '40px',
                          backgroundColor: '#f3f4f6',
                          color: '#6b7280',
                        }}
                      >
                        {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                      </div>
                      <div className="flex-grow-1">
                        <div className="fw-medium">
                          {user.first_name} {user.last_name}
                        </div>
                        <small className="text-muted">{user.email}</small>
                      </div>
                      <Badge
                        bg={user.role === 'admin' ? 'danger' : user.role === 'teacher' ? 'primary' : 'secondary'}
                        className="text-capitalize"
                      >
                        {user.role}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <Users size={48} className="text-muted mb-3" />
                  <p className="text-muted">No users found</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
