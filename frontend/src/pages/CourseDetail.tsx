import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, Tab, Nav } from 'react-bootstrap';
import { ArrowLeft, Play, BookOpen, Clock, Users } from 'lucide-react';
import { useCourse } from '../hooks/useCourses';
import QuizList from '../components/QuizList';
import { quizService } from '../services/quizService';
import type { Quiz } from '../types';

const CourseDetail: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();

  const courseIdNum = courseId ? parseInt(courseId, 10) : 0;
  const { course, loading, error } = useCourse(courseIdNum);

  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [loadingQuizzes, setLoadingQuizzes] = useState(false);

  const handleStartLesson = (lessonId: number) => {
    navigate(`/lessons/${lessonId}`);
  };

  const handleBackToCourses = () => {
    navigate('/courses');
  };

  useEffect(() => {
    const fetchQuizzes = async () => {
      if (!courseId) return;

      try {
        setLoadingQuizzes(true);
        const quizzesData = await quizService.getQuizzesByCourse(parseInt(courseId));
        setQuizzes(quizzesData);
      } catch (error) {
        console.error('Failed to fetch quizzes:', error);
      } finally {
        setLoadingQuizzes(false);
      }
    };

    fetchQuizzes();
  }, [courseId]);

  if (!courseId || isNaN(courseIdNum)) {
    return (
      <Container>
        <Alert variant="danger">
          <strong>Invalid course ID</strong>
          <div className="mt-2">
            <Button variant="outline-secondary" onClick={handleBackToCourses}>
              <ArrowLeft size={16} className="me-1" />
              Back to Courses
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container>
        <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading course...</span>
          </Spinner>
        </div>
      </Container>
    );
  }

  if (error || !course) {
    return (
      <Container>
        <Alert variant="danger">
          <strong>Error loading course:</strong> {error || 'Course not found'}
          <div className="mt-2">
            <Button variant="outline-secondary" onClick={handleBackToCourses}>
              <ArrowLeft size={16} className="me-1" />
              Back to Courses
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Container>
      {/* Course Header */}
      <div className="mb-4">
        <Button variant="outline-secondary" onClick={handleBackToCourses} className="mb-3">
          <ArrowLeft size={16} className="me-1" />
          Back to Courses
        </Button>

        <Row>
          <Col lg={8}>
            <div className="d-flex align-items-start mb-3">
              <div className="flex-grow-1">
                <h1 className="mb-2">{course.title}</h1>
                <p className="text-muted mb-3">{course.description}</p>

                <div className="d-flex align-items-center gap-3 mb-3">
                  <Badge bg={course.is_published ? 'success' : 'warning'}>
                    {course.is_published ? 'Published' : 'Draft'}
                  </Badge>

                  {course.creator && (
                    <div className="d-flex align-items-center text-muted">
                      <Users size={16} className="me-1" />
                      <span>Instructor: {course.creator.first_name} {course.creator.last_name}</span>
                    </div>
                  )}

                  <div className="d-flex align-items-center text-muted">
                    <Clock size={16} className="me-1" />
                    <span>Created {formatDate(course.created_at)}</span>
                  </div>
                </div>
              </div>
            </div>
          </Col>

          <Col lg={4}>
            {course.thumbnail && (
              <img
                src={course.thumbnail}
                alt={course.title}
                className="img-fluid rounded"
                style={{ maxHeight: '200px', width: '100%', objectFit: 'cover' }}
              />
            )}
          </Col>
        </Row>
      </div>

      {/* Course Content */}
      <Tab.Container defaultActiveKey="content">
        <Nav variant="tabs" className="mb-3">
          <Nav.Item>
            <Nav.Link eventKey="content">Course Content</Nav.Link>
          </Nav.Item>
          <Nav.Item>
            <Nav.Link eventKey="quizzes">Quizzes</Nav.Link>
          </Nav.Item>
          {/* Các tabs khác nếu có */}
        </Nav>

        <Tab.Content>
          <Tab.Pane eventKey="content">
            <Row>
              <Col lg={8}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0 d-flex align-items-center">
                      <BookOpen size={20} className="me-2" />
                      Lessons ({course.lessons?.length || 0})
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    {course.lessons && course.lessons.length > 0 ? (
                      <div className="lesson-list">
                        {course.lessons
                          .sort((a, b) => a.order_index - b.order_index)
                          .map((lesson, index) => (
                            <div key={lesson.id} className="lesson-item mb-3 p-3 border rounded">
                              <div className="d-flex justify-content-between align-items-start">
                                <div className="flex-grow-1">
                                  <h6 className="mb-1">
                                    {index + 1}. {lesson.title}
                                  </h6>
                                  {lesson.description && (
                                    <p className="text-muted small mb-2">{lesson.description}</p>
                                  )}
                                  <div className="d-flex align-items-center text-muted small">
                                    <BookOpen size={14} className="me-1" />
                                    <span>{lesson.slides?.length || 0} slides</span>
                                  </div>
                                </div>
                                <div className="ms-3">
                                  <Button
                                    variant="primary"
                                    size="sm"
                                    onClick={() => handleStartLesson(lesson.id)}
                                  >
                                    <Play size={14} className="me-1" />
                                    Start Lesson
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <BookOpen size={48} className="text-muted mb-3" />
                        <h6>No lessons available</h6>
                        <p className="text-muted">This course doesn't have any lessons yet.</p>
                      </div>
                    )}
                  </Card.Body>
                </Card>
              </Col>

              <Col lg={4}>
                <Card>
                  <Card.Header>
                    <h6 className="mb-0">Course Information</h6>
                  </Card.Header>
                  <Card.Body>
                    <div className="course-stats">
                      <div className="stat-item mb-3">
                        <div className="d-flex justify-content-between">
                          <span className="text-muted">Total Lessons:</span>
                          <strong>{course.lessons?.length || 0}</strong>
                        </div>
                      </div>

                      <div className="stat-item mb-3">
                        <div className="d-flex justify-content-between">
                          <span className="text-muted">Total Slides:</span>
                          <strong>
                            {course.lessons?.reduce((total, lesson) =>
                              total + (lesson.slides?.length || 0), 0
                            ) || 0}
                          </strong>
                        </div>
                      </div>

                      <div className="stat-item mb-3">
                        <div className="d-flex justify-content-between">
                          <span className="text-muted">Quizzes:</span>
                          <strong>{course.quizzes?.length || 0}</strong>
                        </div>
                      </div>

                      <div className="stat-item mb-3">
                        <div className="d-flex justify-content-between">
                          <span className="text-muted">Status:</span>
                          <Badge bg={course.is_published ? 'success' : 'warning'}>
                            {course.is_published ? 'Published' : 'Draft'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Tab.Pane>
          <Tab.Pane eventKey="quizzes">
            {loadingQuizzes ? (
              <div className="text-center p-4">Loading quizzes...</div>
            ) : (
              <QuizList quizzes={quizzes} courseId={Number(courseId)} />
            )}
          </Tab.Pane>
        </Tab.Content>
      </Tab.Container>
    </Container>
  );
};

export default CourseDetail;
