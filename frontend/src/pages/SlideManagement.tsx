import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Container, 
  Row, 
  Col, 
  Card, 
  Button, 
  Alert, 
  Breadcrumb, 
  Table,
  Badge,
  Dropdown,
  Modal,
  Spinner
} from 'react-bootstrap';
import { 
  ArrowLeft, 
  Plus, 
  Edit3, 
  Trash2, 
  Eye, 
  MoreVertical,
  FileText
} from 'lucide-react';
import { slideService } from '../services/slideService';
import { lessonService } from '../services/lessonService';
import { courseService } from '../services/courseService';
import { type Slide, type Lesson, type Course } from '../types';

const SlideManagement: React.FC = () => {
  const navigate = useNavigate();
  const { lessonId } = useParams<{ lessonId: string }>();
  
  const [slides, setSlides] = useState<Slide[]>([]);
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModal, setDeleteModal] = useState<{ show: boolean; slide: Slide | null }>({
    show: false,
    slide: null
  });

  useEffect(() => {
    const fetchData = async () => {
      if (!lessonId) {
        setError('Lesson ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        
        // Fetch lesson data
        const lessonData = await lessonService.getLessonById(parseInt(lessonId));
        setLesson(lessonData);
        
        // Fetch course data
        if (lessonData.course_id) {
          const courseData = await courseService.getCourseById(lessonData.course_id);
          setCourse(courseData);
        }
        
        // Fetch slides
        const slidesData = await slideService.getSlidesByLessonId(parseInt(lessonId));
        setSlides(slidesData);
        
      } catch (err) {
        setError('Failed to load data');
        console.error('Error fetching data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [lessonId]);

  const handleCreateSlide = () => {
    navigate(`/lessons/${lessonId}/slides/create`);
  };

  const handleEditSlide = (slideId: number) => {
    navigate(`/slides/${slideId}/edit`);
  };

  const handleViewSlides = () => {
    navigate(`/lessons/${lessonId}`);
  };

  const handleDeleteSlide = async (slide: Slide) => {
    try {
      await slideService.deleteSlide(slide.id);
      setSlides(prev => prev.filter(s => s.id !== slide.id));
      setDeleteModal({ show: false, slide: null });
    } catch (err) {
      console.error('Error deleting slide:', err);
      setError('Failed to delete slide');
    }
  };

  const handleBack = () => {
    if (course) {
      navigate(`/courses/${course.id}`);
    } else {
      navigate('/courses');
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">
          <strong>Error:</strong> {error}
        </Alert>
        <Button variant="outline-secondary" onClick={handleBack}>
          <ArrowLeft size={16} className="me-1" />
          Back
        </Button>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <Button 
                variant="outline-secondary" 
                size="sm" 
                onClick={handleBack}
                className="mb-2"
              >
                <ArrowLeft size={16} className="me-1" />
                Back to Course
              </Button>
              <h2 className="mb-1">
                <FileText size={24} className="me-2" />
                Manage Slides
              </h2>
              <Breadcrumb className="mb-0">
                <Breadcrumb.Item href="/courses">Courses</Breadcrumb.Item>
                {course && (
                  <Breadcrumb.Item href={`/courses/${course.id}`}>
                    {course.title}
                  </Breadcrumb.Item>
                )}
                <Breadcrumb.Item active>
                  {lesson?.title} - Slides
                </Breadcrumb.Item>
              </Breadcrumb>
            </div>
            <div>
              <Button 
                variant="outline-primary" 
                onClick={handleViewSlides}
                className="me-2"
              >
                <Eye size={16} className="me-1" />
                View Slides
              </Button>
              <Button 
                variant="primary" 
                onClick={handleCreateSlide}
              >
                <Plus size={16} className="me-1" />
                Create Slide
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Slides List */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                Slides ({slides.length})
              </h5>
            </Card.Header>
            <Card.Body className="p-0">
              {slides.length === 0 ? (
                <div className="text-center py-5">
                  <FileText size={48} className="text-muted mb-3" />
                  <h5 className="text-muted">No slides yet</h5>
                  <p className="text-muted">Create your first slide to get started.</p>
                  <Button variant="primary" onClick={handleCreateSlide}>
                    <Plus size={16} className="me-1" />
                    Create First Slide
                  </Button>
                </div>
              ) : (
                <Table responsive hover className="mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>Order</th>
                      <th>Title</th>
                      <th>Content Preview</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {slides
                      .sort((a, b) => a.order_index - b.order_index)
                      .map((slide, index) => (
                        <tr key={slide.id}>
                          <td>
                            <Badge bg="secondary">#{slide.order_index + 1}</Badge>
                          </td>
                          <td>
                            <strong>{slide.title || 'Untitled Slide'}</strong>
                          </td>
                          <td>
                            <div 
                              className="text-muted small"
                              style={{ 
                                maxWidth: '300px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {slide.content.substring(0, 100)}
                              {slide.content.length > 100 && '...'}
                            </div>
                          </td>
                          <td>
                            <small className="text-muted">
                              {new Date(slide.created_at).toLocaleDateString()}
                            </small>
                          </td>
                          <td>
                            <Dropdown>
                              <Dropdown.Toggle 
                                variant="outline-secondary" 
                                size="sm"
                                className="border-0"
                              >
                                <MoreVertical size={16} />
                              </Dropdown.Toggle>
                              <Dropdown.Menu>
                                <Dropdown.Item onClick={() => handleEditSlide(slide.id)}>
                                  <Edit3 size={16} className="me-2" />
                                  Edit
                                </Dropdown.Item>
                                <Dropdown.Divider />
                                <Dropdown.Item 
                                  className="text-danger"
                                  onClick={() => setDeleteModal({ show: true, slide })}
                                >
                                  <Trash2 size={16} className="me-2" />
                                  Delete
                                </Dropdown.Item>
                              </Dropdown.Menu>
                            </Dropdown>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Delete Confirmation Modal */}
      <Modal 
        show={deleteModal.show} 
        onHide={() => setDeleteModal({ show: false, slide: null })}
      >
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete the slide "{deleteModal.slide?.title || 'Untitled'}"? 
          This action cannot be undone.
        </Modal.Body>
        <Modal.Footer>
          <Button 
            variant="secondary" 
            onClick={() => setDeleteModal({ show: false, slide: null })}
          >
            Cancel
          </Button>
          <Button 
            variant="danger" 
            onClick={() => deleteModal.slide && handleDeleteSlide(deleteModal.slide)}
          >
            Delete
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default SlideManagement;
