import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Container, Row, Col, Button, Al<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { ArrowLeft, Edit3 } from 'lucide-react';
import SlideEditor from '../components/SlideEditor';
import { slideService } from '../services/slideService';
import { lessonService } from '../services/lessonService';
import { courseService } from '../services/courseService';
import { type Slide, type Lesson, type Course } from '../types';

const SlideEdit: React.FC = () => {
  const navigate = useNavigate();
  const { slideId } = useParams<{ slideId: string }>();
  const [searchParams] = useSearchParams();
  const fromCourseEdit = searchParams.get('from') === 'course-edit';
  const courseId = searchParams.get('courseId');
  const lessonId = searchParams.get('lessonId');

  const [slide, setSlide] = useState<Slide | null>(null);
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!slideId) {
        setError('Slide ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch slide data
        const slideData = await slideService.getSlide(parseInt(slideId));
        setSlide(slideData);

        // Fetch lesson data
        if (slideData.lesson_id) {
          const lessonData = await lessonService.getLessonById(slideData.lesson_id);
          setLesson(lessonData);

          // Fetch course data
          if (lessonData.course_id) {
            const courseData = await courseService.getCourseById(lessonData.course_id);
            setCourse(courseData);
          }
        }
      } catch (err) {
        setError('Failed to load slide data');
        console.error('Error fetching slide data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [slideId]);

  const handleSave = async (title: string, content: string) => {
    if (!slide) {
      setError('Slide data not available');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const updatedSlide = await slideService.updateSlide(slide.id, {
        title,
        content,
      });

      setSlide(updatedSlide);

      // Navigate back based on where we came from
      if (fromCourseEdit && courseId) {
        navigate(`/courses/${courseId}/edit?tab=content`);
      } else if (lesson) {
        navigate(`/lessons/${lesson.id}`);
      } else {
        navigate('/courses');
      }
    } catch (err) {
      setError('Failed to update slide. Please try again.');
      console.error('Error updating slide:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (fromCourseEdit && courseId) {
      navigate(`/courses/${courseId}/edit?tab=content`);
    } else if (lesson) {
      navigate(`/lessons/${lesson.id}`);
    } else {
      navigate('/courses');
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading slide...</span>
        </Spinner>
      </Container>
    );
  }

  if (error && !slide) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">
          <strong>Error:</strong> {error}
        </Alert>
        <Button variant="outline-secondary" onClick={() => navigate('/courses')}>
          <ArrowLeft size={16} className="me-1" />
          Back to Courses
        </Button>
      </Container>
    );
  }

  return (
    <div className="slide-edit-page" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Container fluid className="bg-light border-bottom py-3">
        <Row>
          <Col>
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={handleCancel}
                  className="me-3"
                >
                  <ArrowLeft size={16} className="me-1" />
                  Back
                </Button>
                <div>
                  <h4 className="mb-0">
                    <Edit3 size={20} className="me-2" />
                    Edit Slide
                  </h4>
                  <Breadcrumb className="mb-0 small">
                    <Breadcrumb.Item href="/courses">Courses</Breadcrumb.Item>
                    {course && (
                      <Breadcrumb.Item href={`/courses/${course.id}`}>
                        {course.title}
                      </Breadcrumb.Item>
                    )}
                    {lesson && (
                      <Breadcrumb.Item href={`/lessons/${lesson.id}`}>
                        {lesson.title}
                      </Breadcrumb.Item>
                    )}
                    <Breadcrumb.Item active>
                      Edit: {slide?.title || 'Untitled Slide'}
                    </Breadcrumb.Item>
                  </Breadcrumb>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Container>

      {/* Editor */}
      <div className="flex-grow-1">
        <SlideEditor
          initialTitle={slide?.title || ''}
          initialContent={slide?.content || ''}
          onSave={handleSave}
          onCancel={handleCancel}
          loading={saving}
          error={error}
        />
      </div>
    </div>
  );
};

export default SlideEdit;
