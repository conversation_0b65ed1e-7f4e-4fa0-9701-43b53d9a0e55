import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Container, Row, Col, Card, Form, Button, Alert, Breadcrumb } from 'react-bootstrap';
import { ArrowLeft, Plus } from 'lucide-react';
import SlideEditor from '../components/SlideEditor';
import { slideService } from '../services/slideService';
import { lessonService } from '../services/lessonService';
import { courseService } from '../services/courseService';
import { type Lesson, type Course } from '../types';

const SlideCreate: React.FC = () => {
  const navigate = useNavigate();
  const { lessonId } = useParams<{ lessonId: string }>();
  const [searchParams] = useSearchParams();
  const courseId = searchParams.get('courseId');
  const fromCourseEdit = searchParams.get('from') === 'course-edit';

  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState('');

  // Debug URL parameters
  console.log('SlideCreate URL params:', {
    lessonId,
    courseId,
    fromCourseEdit,
    allSearchParams: Object.fromEntries(searchParams.entries()),
    currentURL: window.location.href
  });



  useEffect(() => {
    const fetchData = async () => {
      try {
        if (lessonId) {
          const lessonData = await lessonService.getLessonById(parseInt(lessonId));
          setLesson(lessonData);

          if (lessonData.course_id) {
            const courseData = await courseService.getCourseById(lessonData.course_id);
            setCourse(courseData);
          }
        } else if (courseId) {
          const courseData = await courseService.getCourseById(parseInt(courseId));
          setCourse(courseData);
        }
      } catch (err) {
        setError('Failed to load lesson or course data');
        console.error('Error fetching data:', err);
      }
    };

    fetchData();
  }, [lessonId, courseId]);

  const handleSave = async (title: string, content: string) => {
    if (!lessonId) {
      setError('Lesson ID is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get current slides to determine order_index
      const existingSlides = await slideService.getSlidesByLessonId(parseInt(lessonId));
      console.log('Existing slides:', existingSlides);
      console.log('Existing slides type:', typeof existingSlides);
      console.log('Existing slides is array:', Array.isArray(existingSlides));

      let nextOrderIndex = 1; // Default to 1

      // Ensure existingSlides is a valid array
      const validSlides = Array.isArray(existingSlides) ? existingSlides : [];

      console.log('Processing slides for order calculation:', {
        validSlidesLength: validSlides.length,
        validSlides: validSlides
      });

      if (validSlides.length === 0) {
        // No existing slides, start with 1
        nextOrderIndex = 1;
        console.log('No existing slides, setting order_index to 1');
      } else {
        // Filter out slides with invalid order_index and ensure we have valid numbers
        const validOrderIndexes = validSlides
          .map(slide => slide.order_index)
          .filter(index => typeof index === 'number' && !isNaN(index) && index > 0);

        console.log('Valid order indexes found:', validOrderIndexes);

        if (validOrderIndexes.length > 0) {
          const maxOrder = Math.max(...validOrderIndexes);
          nextOrderIndex = maxOrder + 1;
          console.log('Calculated next order index from max:', { maxOrder, nextOrderIndex });
        } else {
          // All existing slides have invalid order_index, start with 1
          nextOrderIndex = 1;
          console.log('All existing slides have invalid order_index, setting to 1');
        }
      }

      // Final safety check
      if (typeof nextOrderIndex !== 'number' || isNaN(nextOrderIndex) || nextOrderIndex < 1) {
        console.log('Final safety check failed, forcing order_index to 1');
        nextOrderIndex = 1;
      }

      console.log('Order index calculation:', {
        existingSlidesLength: validSlides.length,
        existingOrderIndexes: validSlides.map(s => s?.order_index),
        calculatedNextOrderIndex: nextOrderIndex
      });

      // Force nextOrderIndex to be at least 1
      const finalOrderIndex = Math.max(1, nextOrderIndex);

      console.log('Final order index check:', {
        nextOrderIndex,
        finalOrderIndex,
        mathMaxResult: Math.max(1, nextOrderIndex)
      });

      const slideData = {
        lesson_id: parseInt(lessonId),
        title,
        content,
        image_url: imageUrl || undefined,
        order_index: finalOrderIndex,
      };

      console.log('Creating slide with data:', slideData);
      console.log('slideData.order_index type:', typeof slideData.order_index);
      console.log('slideData.order_index value:', slideData.order_index);
      console.log('Navigation params:', { fromCourseEdit, courseId, lessonId });

      await slideService.createSlide(slideData);

      // Navigate back based on where we came from
      console.log('Navigation decision:', {
        fromCourseEdit,
        courseId,
        lesson: lesson?.id,
        lessonId
      });

      // Simplified navigation logic
      const from = searchParams.get('from');
      const courseIdParam = searchParams.get('courseId');

      console.log('URL params check:', { from, courseIdParam });

      if (from === 'course-edit' && courseIdParam) {
        const targetUrl = `/courses/${courseIdParam}/edit?tab=content`;
        console.log('Navigating to course edit:', targetUrl);
        navigate(targetUrl);
      } else if (lesson) {
        console.log('Navigating to lesson:', `/lessons/${lessonId}`);
        navigate(`/lessons/${lessonId}`);
      } else {
        console.log('Navigating to courses list');
        navigate('/courses');
      }
    } catch (err) {
      setError('Failed to create slide. Please try again.');
      console.error('Error creating slide:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    console.log('Cancel navigation decision:', {
      fromCourseEdit,
      courseId,
      lesson: lesson?.id,
      lessonId
    });

    // Simplified navigation logic
    const from = searchParams.get('from');
    const courseIdParam = searchParams.get('courseId');

    console.log('Cancel URL params check:', { from, courseIdParam });

    if (from === 'course-edit' && courseIdParam) {
      const targetUrl = `/courses/${courseIdParam}/edit?tab=content`;
      console.log('Cancel: Navigating to course edit:', targetUrl);
      navigate(targetUrl);
    } else if (lesson) {
      console.log('Cancel: Navigating to lesson:', `/lessons/${lessonId}`);
      navigate(`/lessons/${lessonId}`);
    } else {
      console.log('Cancel: Navigating to courses list');
      navigate('/courses');
    }
  };

  return (
    <div className="slide-create-page" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Container fluid className="bg-light border-bottom py-3">
        <Row>
          <Col>
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={handleCancel}
                  className="me-3"
                >
                  <ArrowLeft size={16} className="me-1" />
                  Back
                </Button>
                <div>
                  <h4 className="mb-0">
                    <Plus size={20} className="me-2" />
                    Create New Slide
                  </h4>
                  <Breadcrumb className="mb-0 small">
                    <Breadcrumb.Item href="/courses">Courses</Breadcrumb.Item>
                    {course && (
                      <Breadcrumb.Item href={`/courses/${course.id}`}>
                        {course.title}
                      </Breadcrumb.Item>
                    )}
                    {lesson && (
                      <Breadcrumb.Item href={`/lessons/${lesson.id}`}>
                        {lesson.title}
                      </Breadcrumb.Item>
                    )}
                    <Breadcrumb.Item active>New Slide</Breadcrumb.Item>
                  </Breadcrumb>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Container>

      {/* Optional Image URL Input */}
      <Container fluid className="py-2 bg-white border-bottom">
        <Row>
          <Col md={6}>
            <Form.Group>
              <Form.Label className="small">Image URL (Optional)</Form.Label>
              <Form.Control
                type="url"
                size="sm"
                placeholder="https://example.com/image.jpg"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
              />
              <Form.Text className="text-muted">
                Add an image URL to display in the slide
              </Form.Text>
            </Form.Group>
          </Col>
        </Row>
      </Container>

      {/* Editor */}
      <div className="flex-grow-1">
        <SlideEditor
          onSave={handleSave}
          onCancel={handleCancel}
          loading={loading}
          error={error}
        />
      </div>
    </div>
  );
};

export default SlideCreate;
