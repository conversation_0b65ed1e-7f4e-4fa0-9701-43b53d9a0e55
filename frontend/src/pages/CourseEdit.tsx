import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Form, Alert, Spinner, Nav, Tab, Modal } from 'react-bootstrap';
import { ArrowLeft, Save, Plus, Edit, Trash2, FileText, Eye, Edit3 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { courseService } from '../services/courseService';
import { lessonService } from '../services/lessonService';
import { slideService } from '../services/slideService';
import { type Course, type Lesson, type Slide } from '../types';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import 'github-markdown-css/github-markdown-light.css';

// Custom styles for slide preview
const slidePreviewStyles = `
  .slide-preview .markdown-body {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
    font-size: 14px;
  }

  .slide-preview .markdown-body h1,
  .slide-preview .markdown-body h2,
  .slide-preview .markdown-body h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
  }

  .slide-preview .markdown-body p {
    margin-bottom: 0.5rem;
  }

  .slide-preview .markdown-body pre {
    margin-bottom: 0.5rem;
    font-size: 12px;
  }

  .slide-preview .markdown-body ul,
  .slide-preview .markdown-body ol {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
  }
`;

const CourseEdit: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { state } = useAuth();

  const [course, setCourse] = useState<Course | null>(null);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [selectedLesson, setSelectedLesson] = useState<number | null>(null);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('details');

  const [courseForm, setCourseForm] = useState({
    title: '',
    description: '',
    thumbnail: '',
    is_published: false
  });

  // Đảm bảo state cho newSlideForm được khởi tạo đúng
  const [newSlideForm, setNewSlideForm] = useState({
    title: '',
    content: '',
    image_url: '',
    lesson_id: 0,
    order_index: 0
  });

  // Thêm state cho form tạo bài học mới
  const [newLessonForm, setNewLessonForm] = useState({
    title: '',
    description: '',
    course_id: parseInt(courseId || '0'),
    order_index: 0
  });

  // Thêm state để kiểm soát modal
  const [showLessonModal, setShowLessonModal] = useState(false);

  // Thêm state cho việc chỉnh sửa bài học
  const [editLessonForm, setEditLessonForm] = useState({
    id: 0,
    title: '',
    description: ''
  });
  const [showEditLessonModal, setShowEditLessonModal] = useState(false);

  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';

  useEffect(() => {
    if (!courseId || !isTeacherOrAdmin) {
      navigate('/courses');
      return;
    }
    fetchCourseData();
  }, [courseId, isTeacherOrAdmin, navigate]);

  const fetchCourseData = async () => {
    try {
      setLoading(true);
      const courseData = await courseService.getCourse(parseInt(courseId!));
      setCourse(courseData);

      setCourseForm({
        title: courseData.title,
        description: courseData.description,
        thumbnail: courseData.thumbnail || '',
        is_published: courseData.is_published
      });

      const lessonsData = await lessonService.getLessonsByCourse(parseInt(courseId!));
      setLessons(lessonsData);

      if (lessonsData.length > 0 && !selectedLesson) {
        setSelectedLesson(lessonsData[0].id);
        fetchSlides(lessonsData[0].id);
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load course');
    } finally {
      setLoading(false);
    }
  };

  // Cập nhật hàm fetchSlides để đảm bảo order_index bắt đầu từ 1
  const fetchSlides = async (lessonId: number) => {
    try {
      const slidesData = await slideService.getSlidesByLesson(lessonId);
      setSlides(slidesData);

      // Tính toán order_index mới, bắt đầu từ 1
      const nextOrderIndex = slidesData.length > 0
        ? Math.max(...slidesData.map(slide => slide.order_index)) + 1
        : 1; // Bắt đầu từ 1 nếu không có slide nào

      // Cập nhật form với lesson_id và order_index mới
      setNewSlideForm({
        title: '',
        content: '',
        image_url: '',
        lesson_id: lessonId,
        order_index: nextOrderIndex
      });
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load slides');
    }
  };

  const handleSaveCourse = async () => {
    try {
      setSaving(true);
      await courseService.updateCourse(parseInt(courseId!), courseForm);
      await fetchCourseData();
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to save course');
    } finally {
      setSaving(false);
    }
  };

  // Cập nhật lại hàm handleLessonSelect để đảm bảo lesson_id được cập nhật đúng
  const handleLessonSelect = (lessonId: number) => {
    setSelectedLesson(lessonId);
    fetchSlides(lessonId);

    // Cập nhật lesson_id trong form
    setNewSlideForm(prev => ({
      ...prev,
      lesson_id: lessonId
    }));
  };

  // Sửa lại hàm handleAddSlide để đảm bảo order_index bắt đầu từ 1
  const handleAddSlide = async () => {
    if (!selectedLesson) {
      setError('Please select a lesson first');
      return;
    }

    if (!newSlideForm.content.trim()) {
      setError('Slide content is required');
      return;
    }

    try {
      setSaving(true);
      setError(null);

      // Tính toán order_index mới, bắt đầu từ 1
      const nextOrderIndex = slides.length > 0
        ? Math.max(...slides.map(slide => slide.order_index)) + 1
        : 1; // Bắt đầu từ 1 nếu không có slide nào

      // Log để debug
      console.log('Creating slide with data:', {
        title: newSlideForm.title,
        content: newSlideForm.content,
        image_url: newSlideForm.image_url,
        lesson_id: selectedLesson,
        order_index: nextOrderIndex
      });

      // Gọi API với dữ liệu đầy đủ và đảm bảo order_index được gửi
      await slideService.createSlide({
        title: newSlideForm.title,
        content: newSlideForm.content,
        image_url: newSlideForm.image_url,
        lesson_id: selectedLesson,
        order_index: nextOrderIndex
      });

      // Reset form
      setNewSlideForm({
        title: '',
        content: '',
        image_url: '',
        lesson_id: selectedLesson,
        order_index: 1 // Đặt giá trị mặc định là 1
      });

      // Tải lại danh sách slides
      await fetchSlides(selectedLesson);
    } catch (err: any) {
      console.error('Error creating slide:', err);
      setError(err.response?.data?.error || 'Failed to add slide');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteSlide = async (slideId: number) => {
    if (!window.confirm('Are you sure you want to delete this slide?')) {
      return;
    }

    try {
      await slideService.deleteSlide(slideId);
      if (selectedLesson) {
        await fetchSlides(selectedLesson);
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete slide');
    }
  };

  // Hàm xử lý thêm bài học mới
  const handleAddLesson = async () => {
    try {
      setSaving(true);

      // Đảm bảo order_index được tính đúng, bắt đầu từ 1
      const nextOrderIndex = lessons.length > 0
        ? Math.max(...lessons.map(lesson => lesson.order_index)) + 1
        : 1; // Bắt đầu từ 1 nếu không có lesson nào

      await lessonService.createLesson({
        ...newLessonForm,
        course_id: parseInt(courseId!),
        order_index: nextOrderIndex
      });

      // Reset form
      setNewLessonForm({
        title: '',
        description: '',
        course_id: parseInt(courseId!),
        order_index: 1 // Đặt giá trị mặc định là 1
      });

      setShowLessonModal(false);
      await fetchCourseData();
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add lesson');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteLesson = async (lessonId: number) => {
    if (!window.confirm('Are you sure you want to delete this lesson? All slides in this lesson will also be deleted.')) {
      return;
    }

    try {
      setSaving(true);
      await lessonService.deleteLesson(lessonId);

      // Nếu đang chọn bài học bị xóa, reset selection
      if (selectedLesson === lessonId) {
        setSelectedLesson(null);
        setSlides([]);
      }

      await fetchCourseData();
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete lesson');
    } finally {
      setSaving(false);
    }
  };

  // Sửa lại hàm mở modal để tính order_index bắt đầu từ 1
  const openAddLessonModal = () => {
    // Tính order_index mới, bắt đầu từ 1
    const nextOrderIndex = lessons.length > 0
      ? Math.max(...lessons.map(lesson => lesson.order_index)) + 1
      : 1; // Bắt đầu từ 1 nếu không có lesson nào

    setNewLessonForm({
      title: '',
      description: '',
      course_id: parseInt(courseId || '0'),
      order_index: nextOrderIndex
    });

    setShowLessonModal(true);
  };

  // Hàm mở modal chỉnh sửa bài học
  const openEditLessonModal = (lesson: Lesson) => {
    setEditLessonForm({
      id: lesson.id,
      title: lesson.title,
      description: lesson.description || ''
    });
    setShowEditLessonModal(true);
  };

  // Hàm xử lý cập nhật bài học
  const handleUpdateLesson = async () => {
    try {
      setSaving(true);

      await lessonService.updateLesson(editLessonForm.id, {
        title: editLessonForm.title,
        description: editLessonForm.description
      });

      setShowEditLessonModal(false);
      await fetchCourseData();
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update lesson');
    } finally {
      setSaving(false);
    }
  };

  // Hàm điều hướng đến Slide Editor để tạo slide mới
  const handleCreateSlideWithEditor = () => {
    if (!selectedLesson) {
      setError('Please select a lesson first');
      return;
    }
    navigate(`/lessons/${selectedLesson}/slides/create?from=course-edit&courseId=${courseId}`);
  };

  // Hàm điều hướng đến Slide Editor để chỉnh sửa slide
  const handleEditSlideWithEditor = (slideId: number) => {
    navigate(`/slides/${slideId}/edit?from=course-edit&courseId=${courseId}&lessonId=${selectedLesson}`);
  };

  // Hàm điều hướng đến trang quản lý slides
  const handleManageSlides = () => {
    if (!selectedLesson) {
      setError('Please select a lesson first');
      return;
    }
    navigate(`/lessons/${selectedLesson}/slides`);
  };

  // Hàm xem slides
  const handleViewSlides = () => {
    if (!selectedLesson) {
      setError('Please select a lesson first');
      return;
    }
    navigate(`/lessons/${selectedLesson}`);
  };

  if (!isTeacherOrAdmin) {
    return (
      <Container>
        <Alert variant="danger">
          <strong>Access Denied:</strong> You don't have permission to edit courses.
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container>
        <div className="text-center py-5">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="text-muted">Loading course...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      {/* Inject custom styles */}
      <style>{slidePreviewStyles}</style>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2>Edit Course</h2>
          {course && (
            <p className="text-muted mb-0">{course.title}</p>
          )}
        </div>
        <Button variant="outline-secondary" onClick={() => navigate('/courses')}>
          <ArrowLeft size={16} className="me-1" />
          Back to Courses
        </Button>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4" dismissible onClose={() => setError(null)}>
          <strong>Error:</strong> {error}
        </Alert>
      )}

      <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'details')}>
        <Nav variant="tabs" className="mb-4">
          <Nav.Item>
            <Nav.Link eventKey="details">Course Details</Nav.Link>
          </Nav.Item>
          <Nav.Item>
            <Nav.Link eventKey="content">Lessons & Slides</Nav.Link>
          </Nav.Item>
        </Nav>

        <Tab.Content>
          <Tab.Pane eventKey="details">
            <Card>
              <Card.Body>
                <Form>
                  <Form.Group className="mb-3">
                    <Form.Label>Course Title</Form.Label>
                    <Form.Control
                      type="text"
                      value={courseForm.title}
                      onChange={(e) => setCourseForm({...courseForm, title: e.target.value})}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={4}
                      value={courseForm.description}
                      onChange={(e) => setCourseForm({...courseForm, description: e.target.value})}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Thumbnail URL</Form.Label>
                    <Form.Control
                      type="text"
                      value={courseForm.thumbnail}
                      onChange={(e) => setCourseForm({...courseForm, thumbnail: e.target.value})}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Check
                      type="checkbox"
                      label="Published"
                      checked={courseForm.is_published}
                      onChange={(e) => setCourseForm({...courseForm, is_published: e.target.checked})}
                    />
                  </Form.Group>

                  <Button
                    variant="primary"
                    onClick={handleSaveCourse}
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <Spinner as="span" animation="border" size="sm" className="me-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save size={16} className="me-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          </Tab.Pane>

          <Tab.Pane eventKey="content">
            <Row>
              <Col md={4}>
                <Card className="mb-4">
                  <Card.Header className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">Lessons</h5>
                    <Button variant="primary" size="sm" onClick={openAddLessonModal}>
                      <Plus size={16} className="me-1" />
                      Add Lesson
                    </Button>
                  </Card.Header>
                  <Card.Body>
                    {lessons.length === 0 ? (
                      <p className="text-muted">No lessons added yet.</p>
                    ) : (
                      <Nav variant="pills" className="flex-column">
                        {lessons.map((lesson) => (
                          <Nav.Item key={lesson.id}>
                            <Nav.Link
                              active={selectedLesson === lesson.id}
                              onClick={() => handleLessonSelect(lesson.id)}
                              className="mb-2"
                            >
                              <div className="d-flex justify-content-between align-items-start">
                                <div>
                                  <div className="d-flex align-items-center">
                                    <span className="me-2">{lesson.title}</span>
                                    <small className="text-muted">({lesson.order_index})</small>
                                  </div>
                                  {lesson.description && (
                                    <small className="text-muted d-block mt-1">{lesson.description}</small>
                                  )}
                                </div>
                                <div className="d-flex align-items-center">
                                  <Button
                                    variant="outline-secondary"
                                    size="sm"
                                    className="me-1"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      openEditLessonModal(lesson);
                                    }}
                                  >
                                    <Edit size={14} />
                                  </Button>
                                  <Button
                                    variant="outline-danger"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteLesson(lesson.id);
                                    }}
                                  >
                                    <Trash2 size={14} />
                                  </Button>
                                </div>
                              </div>
                            </Nav.Link>
                          </Nav.Item>
                        ))}
                      </Nav>
                    )}
                  </Card.Body>
                </Card>
              </Col>

              <Col md={8}>
                <Card>
                  <Card.Header className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">
                      <FileText size={20} className="me-2" />
                      Slides
                    </h5>
                    {selectedLesson && (
                      <div className="d-flex gap-2">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={handleViewSlides}
                        >
                          <Eye size={16} className="me-1" />
                          View
                        </Button>
                        <Button
                          variant="outline-secondary"
                          size="sm"
                          onClick={handleManageSlides}
                        >
                          <Edit size={16} className="me-1" />
                          Manage
                        </Button>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={handleCreateSlideWithEditor}
                        >
                          <Edit3 size={16} className="me-1" />
                          Create with Editor
                        </Button>
                      </div>
                    )}
                  </Card.Header>
                  <Card.Body>
                    {!selectedLesson ? (
                      <Alert variant="info">Please select a lesson to manage slides</Alert>
                    ) : (
                      <>
                        <div className="slides-list mb-4">
                          {slides.length === 0 ? (
                            <div className="text-center py-4">
                              <FileText size={48} className="text-muted mb-3" />
                              <h6 className="text-muted">No slides yet</h6>
                              <p className="text-muted mb-3">Create your first slide to get started.</p>
                              <Button
                                variant="primary"
                                onClick={handleCreateSlideWithEditor}
                              >
                                <Edit3 size={16} className="me-1" />
                                Create with Markdown Editor
                              </Button>
                            </div>
                          ) : (
                            slides
                              .sort((a, b) => a.order_index - b.order_index)
                              .map((slide) => (
                                <Card key={slide.id} className="mb-3">
                                  <Card.Body>
                                    <div className="d-flex justify-content-between align-items-start mb-3">
                                      <div className="flex-grow-1">
                                        <div className="d-flex align-items-center mb-2">
                                          <h6 className="mb-0 me-2">
                                            {slide.title || 'Untitled Slide'}
                                          </h6>
                                          <span className="badge bg-secondary">#{slide.order_index}</span>
                                        </div>
                                        <small className="text-muted">
                                          Created: {new Date(slide.created_at).toLocaleDateString()}
                                        </small>
                                      </div>
                                      <div className="d-flex gap-2">
                                        <Button
                                          variant="outline-primary"
                                          size="sm"
                                          onClick={() => handleEditSlideWithEditor(slide.id)}
                                          title="Edit with Markdown Editor"
                                        >
                                          <Edit3 size={14} />
                                        </Button>
                                        <Button
                                          variant="outline-danger"
                                          size="sm"
                                          onClick={() => handleDeleteSlide(slide.id)}
                                          title="Delete slide"
                                        >
                                          <Trash2 size={14} />
                                        </Button>
                                      </div>
                                    </div>

                                    {/* Markdown Preview */}
                                    <div className="slide-preview">
                                      <div
                                        className="markdown-body"
                                        style={{
                                          maxHeight: '200px',
                                          overflow: 'hidden',
                                          fontSize: '14px',
                                          position: 'relative'
                                        }}
                                      >
                                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                          {slide.content.length > 300
                                            ? slide.content.substring(0, 300) + '...'
                                            : slide.content
                                          }
                                        </ReactMarkdown>
                                        {slide.content.length > 300 && (
                                          <div
                                            className="position-absolute bottom-0 end-0 bg-white px-2"
                                            style={{
                                              background: 'linear-gradient(to right, transparent, white 50%)',
                                              fontSize: '12px'
                                            }}
                                          >
                                            <Button
                                              variant="link"
                                              size="sm"
                                              className="p-0"
                                              onClick={() => handleEditSlideWithEditor(slide.id)}
                                            >
                                              View more...
                                            </Button>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </Card.Body>
                                </Card>
                              ))
                          )}
                        </div>

                        <Card className="border-primary">
                          <Card.Header className="bg-light">
                            <h6 className="mb-0">Quick Add Slide</h6>
                          </Card.Header>
                          <Card.Body>
                            <div className="text-center mb-3">
                              <p className="text-muted mb-3">
                                Choose how you want to create your slide:
                              </p>
                              <div className="d-grid gap-2">
                                <Button
                                  variant="primary"
                                  size="lg"
                                  onClick={handleCreateSlideWithEditor}
                                  className="d-flex align-items-center justify-content-center"
                                >
                                  <Edit3 size={20} className="me-2" />
                                  Create with Markdown Editor
                                  <small className="d-block text-white-50">
                                    Full-featured editor with live preview
                                  </small>
                                </Button>
                              </div>
                            </div>

                            <hr />

                            <h6 className="mb-3">Or create quickly here:</h6>
                            <Form>
                              <Form.Group className="mb-3">
                                <Form.Label>Title</Form.Label>
                                <Form.Control
                                  type="text"
                                  value={newSlideForm.title}
                                  onChange={(e) => setNewSlideForm({...newSlideForm, title: e.target.value})}
                                  placeholder="Enter slide title (optional)"
                                />
                              </Form.Group>

                              <Form.Group className="mb-3">
                                <Form.Label>Content <span className="text-danger">*</span></Form.Label>
                                <Form.Control
                                  as="textarea"
                                  rows={4}
                                  value={newSlideForm.content}
                                  onChange={(e) => setNewSlideForm({...newSlideForm, content: e.target.value})}
                                  placeholder="Enter slide content in Markdown format..."
                                  required
                                  isInvalid={!newSlideForm.content.trim()}
                                />
                                <Form.Control.Feedback type="invalid">
                                  Content is required
                                </Form.Control.Feedback>
                                <Form.Text className="text-muted">
                                  Supports Markdown: **bold**, *italic*, `code`, etc.
                                </Form.Text>
                              </Form.Group>

                              <Form.Group className="mb-3">
                                <Form.Label>Image URL</Form.Label>
                                <Form.Control
                                  type="text"
                                  value={newSlideForm.image_url}
                                  onChange={(e) => setNewSlideForm({...newSlideForm, image_url: e.target.value})}
                                  placeholder="Enter image URL (optional)"
                                />
                              </Form.Group>

                              <Button
                                variant="outline-primary"
                                onClick={handleAddSlide}
                                disabled={saving || !newSlideForm.content.trim()}
                              >
                                {saving ? (
                                  <>
                                    <Spinner as="span" animation="border" size="sm" className="me-2" />
                                    Adding...
                                  </>
                                ) : (
                                  <>
                                    <Plus size={16} className="me-1" />
                                    Quick Add
                                  </>
                                )}
                              </Button>
                            </Form>
                          </Card.Body>
                        </Card>
                      </>
                    )}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Tab.Pane>
        </Tab.Content>
      </Tab.Container>

      {/* Modal thêm bài học mới */}
      <Modal show={showLessonModal} onHide={() => setShowLessonModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Add New Lesson</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Lesson Title</Form.Label>
              <Form.Control
                type="text"
                value={newLessonForm.title}
                onChange={(e) => setNewLessonForm({...newLessonForm, title: e.target.value})}
                placeholder="Enter lesson title"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={newLessonForm.description}
                onChange={(e) => setNewLessonForm({...newLessonForm, description: e.target.value})}
                placeholder="Enter lesson description"
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowLessonModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddLesson}
            disabled={saving || !newLessonForm.title}
          >
            {saving ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                Saving...
              </>
            ) : (
              <>
                <Plus size={16} className="me-1" />
                Add Lesson
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal chỉnh sửa bài học */}
      <Modal show={showEditLessonModal} onHide={() => setShowEditLessonModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Lesson</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Lesson Title</Form.Label>
              <Form.Control
                type="text"
                value={editLessonForm.title}
                onChange={(e) => setEditLessonForm({...editLessonForm, title: e.target.value})}
                placeholder="Enter lesson title"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={editLessonForm.description}
                onChange={(e) => setEditLessonForm({...editLessonForm, description: e.target.value})}
                placeholder="Enter lesson description"
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditLessonModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleUpdateLesson}
            disabled={saving || !editLessonForm.title}
          >
            {saving ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                Saving...
              </>
            ) : (
              <>
                <Save size={16} className="me-1" />
                Save Changes
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default CourseEdit;
