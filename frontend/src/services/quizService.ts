import api from './authService';
import { type Quiz, type Question, type QuizAttempt } from '../types';

export interface QuizzesResponse {
  quizzes: Quiz[];
}

export interface QuizResponse {
  quiz: Quiz;
}

export interface QuestionsResponse {
  questions: Question[];
}

export interface QuestionResponse {
  question: Question;
}

export interface QuizAttemptResponse {
  attempt: QuizAttempt;
}

export interface CreateQuizRequest {
  course_id: number;
  title: string;
  description: string;
  time_limit: number;
  pass_score: number;
  total_questions: number;
}

export interface UpdateQuizRequest {
  title?: string;
  description?: string;
  time_limit?: number;
  pass_score?: number;
  is_published?: boolean;
  total_questions?: number;
}

export interface CreateQuestionRequest {
  quiz_id: number;
  question_text: string;
  question_type: 'single_choice' | 'multiple_choice';
  image_url?: string;
  points: number;
  order_index: number;
  options: CreateOptionRequest[];
}

export interface CreateOptionRequest {
  option_text: string;
  is_correct: boolean;
  order_index: number;
}

export interface SubmitQuizRequest {
  answers: {
    question_id: number;
    option_ids: number[];
  }[];
}

export const quizService = {
  // Quiz CRUD operations
  async getQuizzesByCourse(courseId: number): Promise<Quiz[]> {
    try {
      const response = await api.get<QuizzesResponse>(`/quizzes/course/${courseId}`);
      return response.data.quizzes || [];
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      throw error;
    }
  },

  async getQuiz(id: number): Promise<Quiz> {
    try {
      const response = await api.get<QuizResponse>(`/quizzes/${id}`);
      return response.data.quiz;
    } catch (error) {
      console.error('Error fetching quiz:', error);
      throw error;
    }
  },

  async createQuiz(quizData: CreateQuizRequest): Promise<Quiz> {
    try {
      const response = await api.post<QuizResponse>('/quizzes', quizData);
      return response.data.quiz;
    } catch (error) {
      console.error('Error creating quiz:', error);
      throw error;
    }
  },

  async updateQuiz(id: number, quizData: UpdateQuizRequest): Promise<Quiz> {
    try {
      const response = await api.put<QuizResponse>(`/quizzes/${id}`, quizData);
      return response.data.quiz;
    } catch (error) {
      console.error('Error updating quiz:', error);
      throw error;
    }
  },

  async deleteQuiz(id: number): Promise<void> {
    try {
      await api.delete(`/quizzes/${id}`);
    } catch (error) {
      console.error('Error deleting quiz:', error);
      throw error;
    }
  },

  // Question operations
  async getQuestionsByQuiz(quizId: number): Promise<Question[]> {
    try {
      const response = await api.get<QuestionsResponse>(`/questions/quiz/${quizId}`);
      return response.data.questions || [];
    } catch (error) {
      console.error('Error fetching questions:', error);
      throw error;
    }
  },

  async createQuestion(questionData: CreateQuestionRequest): Promise<Question> {
    try {
      const response = await api.post<QuestionResponse>('/questions', questionData);
      return response.data.question;
    } catch (error) {
      console.error('Error creating question:', error);
      throw error;
    }
  },

  async deleteQuestion(id: number): Promise<void> {
    try {
      await api.delete(`/questions/${id}`);
    } catch (error) {
      console.error('Error deleting question:', error);
      throw error;
    }
  },

  // Quiz attempt operations
  async startQuizAttempt(quizId: number): Promise<QuizAttempt> {
    try {
      const response = await api.post<QuizAttemptResponse>(`/quizzes/${quizId}/attempt`);
      return response.data.attempt;
    } catch (error) {
      console.error('Error starting quiz attempt:', error);
      throw error;
    }
  },

  async submitQuizAttempt(attemptId: number, answers: SubmitQuizRequest): Promise<QuizAttempt> {
    try {
      const response = await api.post<QuizAttemptResponse>(`/quizzes/attempt/${attemptId}/submit`, answers);
      return response.data.attempt;
    } catch (error) {
      console.error('Error submitting quiz attempt:', error);
      throw error;
    }
  },

  // Dynamic question generation
  async generateQuizQuestions(attemptId: number): Promise<any> {
    try {
      const response = await api.post(`/question-bank/quiz-attempt/${attemptId}/generate`);
      return response.data;
    } catch (error) {
      console.error('Error generating quiz questions:', error);
      throw error;
    }
  },

  async getQuizAttemptQuestions(attemptId: number): Promise<any> {
    try {
      const response = await api.get(`/question-bank/quiz-attempt/${attemptId}/questions`);
      return response.data;
    } catch (error) {
      console.error('Error fetching quiz attempt questions:', error);
      throw error;
    }
  },
};
