import api from './authService';
import { type DashboardStats, type Course, type User } from '../types';

export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    // Mock data for now - replace with actual API calls
    return {
      totalCourses: 12,
      totalStudents: 156,
      totalEnrollments: 234,
      totalQuizzes: 45,
    };
  },

  async getRecentCourses(): Promise<Course[]> {
    try {
      const response = await api.get('/courses?limit=5');
      return response.data.courses || [];
    } catch (error) {
      console.error('Error fetching recent courses:', error);
      return [];
    }
  },

  async getRecentUsers(): Promise<User[]> {
    try {
      const response = await api.get('/admin/users?limit=5');
      return response.data.users || [];
    } catch (error) {
      console.error('Error fetching recent users:', error);
      return [];
    }
  },
};
