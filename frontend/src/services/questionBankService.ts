import api from './authService';
import { 
  type QuestionBankCategory, 
  type BankQuestion, 
  type QuizQuestion,
  type DifficultyLevel 
} from '../types';

// Response interfaces
export interface CategoriesResponse {
  categories: QuestionBankCategory[];
}

export interface CategoryResponse {
  category: QuestionBankCategory;
}

export interface BankQuestionsResponse {
  questions: BankQuestion[];
  total: number;
  page: number;
  limit: number;
}

export interface BankQuestionResponse {
  question: BankQuestion;
}

export interface QuizQuestionsResponse {
  quiz_questions: QuizQuestion[];
}

export interface QuizQuestionResponse {
  quiz_question: QuizQuestion;
}

// Request interfaces
export interface CreateCategoryRequest {
  name: string;
  description: string;
  course_id?: number;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  course_id?: number;
}

export interface CreateBankQuestionRequest {
  category_id: number;
  question_text: string;
  question_type: 'single_choice' | 'multiple_choice';
  image_url?: string;
  points: number;
  difficulty: DifficultyLevel;
  tags: string;
  options: CreateBankOptionRequest[];
}

export interface CreateBankOptionRequest {
  option_text: string;
  is_correct: boolean;
  order_index: number;
}

export interface UpdateBankQuestionRequest {
  category_id?: number;
  question_text?: string;
  question_type?: 'single_choice' | 'multiple_choice';
  image_url?: string;
  points?: number;
  difficulty?: DifficultyLevel;
  tags?: string;
  options?: CreateBankOptionRequest[];
}

export interface AddQuestionToQuizRequest {
  bank_question_id: number;
  order_index: number;
  points?: number;
}

export interface UpdateQuizQuestionRequest {
  order_index?: number;
  points?: number;
}

export interface GetBankQuestionsParams {
  category_id?: number;
  difficulty?: DifficultyLevel;
  question_type?: 'single_choice' | 'multiple_choice';
  search?: string;
  page?: number;
  limit?: number;
}

// Category API functions
export const getQuestionBankCategories = async (courseId?: number): Promise<CategoriesResponse> => {
  const params = courseId ? { course_id: courseId } : {};
  const response = await api.get('/question-bank/categories', { params });
  return response.data;
};

export const getQuestionBankCategory = async (id: number): Promise<CategoryResponse> => {
  const response = await api.get(`/question-bank/categories/${id}`);
  return response.data;
};

export const createQuestionBankCategory = async (data: CreateCategoryRequest): Promise<CategoryResponse> => {
  const response = await api.post('/question-bank/categories', data);
  return response.data;
};

export const updateQuestionBankCategory = async (id: number, data: UpdateCategoryRequest): Promise<CategoryResponse> => {
  const response = await api.put(`/question-bank/categories/${id}`, data);
  return response.data;
};

export const deleteQuestionBankCategory = async (id: number): Promise<void> => {
  await api.delete(`/question-bank/categories/${id}`);
};

// Bank Question API functions
export const getBankQuestions = async (params: GetBankQuestionsParams = {}): Promise<BankQuestionsResponse> => {
  const response = await api.get('/question-bank/questions', { params });
  return response.data;
};

export const getBankQuestion = async (id: number): Promise<BankQuestionResponse> => {
  const response = await api.get(`/question-bank/questions/${id}`);
  return response.data;
};

export const createBankQuestion = async (data: CreateBankQuestionRequest): Promise<BankQuestionResponse> => {
  const response = await api.post('/question-bank/questions', data);
  return response.data;
};

export const updateBankQuestion = async (id: number, data: UpdateBankQuestionRequest): Promise<BankQuestionResponse> => {
  const response = await api.put(`/question-bank/questions/${id}`, data);
  return response.data;
};

export const deleteBankQuestion = async (id: number): Promise<void> => {
  await api.delete(`/question-bank/questions/${id}`);
};

// Quiz Question API functions (for assigning bank questions to quizzes)
export const getQuizQuestions = async (quizId: number): Promise<QuizQuestionsResponse> => {
  const response = await api.get(`/question-bank/quiz/${quizId}/questions`);
  return response.data;
};

export const addQuestionToQuiz = async (quizId: number, data: AddQuestionToQuizRequest): Promise<QuizQuestionResponse> => {
  const response = await api.post(`/question-bank/quiz/${quizId}/questions`, data);
  return response.data;
};

export const updateQuizQuestion = async (id: number, data: UpdateQuizQuestionRequest): Promise<QuizQuestionResponse> => {
  const response = await api.put(`/question-bank/quiz-questions/${id}`, data);
  return response.data;
};

export const removeQuestionFromQuiz = async (id: number): Promise<void> => {
  await api.delete(`/question-bank/quiz-questions/${id}`);
};
