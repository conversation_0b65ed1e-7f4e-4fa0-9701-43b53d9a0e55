import api from './authService';
import { type Lesson } from '../types';

export interface LessonsResponse {
  lessons: Lesson[];
}

export interface LessonResponse {
  lesson: Lesson;
}

export const lessonService = {
  async getLessonsByCourse(courseId: number): Promise<Lesson[]> {
    try {
      const response = await api.get<LessonsResponse>(`/lessons/course/${courseId}`);
      return response.data.lessons || [];
    } catch (error) {
      console.error('Error fetching lessons:', error);
      throw error;
    }
  },

  async createLesson(lessonData: {
    course_id: number;
    title: string;
    description?: string;
    order_index: number;
  }): Promise<Lesson> {
    try {
      const response = await api.post<LessonResponse>('/lessons', lessonData);
      return response.data.lesson;
    } catch (error) {
      console.error('Error creating lesson:', error);
      throw error;
    }
  },

  async updateLesson(id: number, lessonData: {
    title?: string;
    description?: string;
    order_index?: number;
  }): Promise<Lesson> {
    try {
      const response = await api.put<LessonResponse>(`/lessons/${id}`, lessonData);
      return response.data.lesson;
    } catch (error) {
      console.error('Error updating lesson:', error);
      throw error;
    }
  },

  async deleteLesson(id: number): Promise<void> {
    try {
      await api.delete(`/lessons/${id}`);
    } catch (error) {
      console.error('Error deleting lesson:', error);
      throw error;
    }
  }
};