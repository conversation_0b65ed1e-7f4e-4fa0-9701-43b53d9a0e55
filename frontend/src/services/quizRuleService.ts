import api from './authService';
import {
  type QuizRule,
  type QuizAttemptQuestion,
  type DifficultyLevel,
  type BankQuestion
} from '../types';

// Response interfaces
export interface QuizRulesResponse {
  rules: QuizRule[];
}

export interface QuizRuleResponse {
  rule: QuizRule;
}

export interface QuizAttemptQuestionsResponse {
  questions: QuizAttemptQuestion[];
}

export interface GenerateQuestionsResponse {
  message: string;
  questions: QuizAttemptQuestion[];
  total_questions: number;
}

export interface PreviewResult {
  rule: QuizRule;
  available_questions: number;
  sample_questions: BankQuestion[];
}

export interface PreviewResponse {
  preview: PreviewResult[];
}

export interface ValidationResult {
  rule_id: number;
  valid: boolean;
  available_questions: number;
  required_questions: number;
  error?: string;
}

export interface ValidationResponse {
  valid: boolean;
  total_questions: number;
  rules_validation: ValidationResult[];
}

// Request interfaces
export interface CreateQuizRuleRequest {
  category_id: number;
  difficulty: DifficultyLevel;
  question_type: 'single_choice' | 'multiple_choice';
  percentage: number;
  order_index: number;
}

export interface UpdateQuizRuleRequest {
  category_id?: number;
  difficulty?: DifficultyLevel;
  question_type?: 'single_choice' | 'multiple_choice';
  percentage?: number;
  order_index?: number;
}

// Quiz Rule API functions
export const getQuizRules = async (quizId: number): Promise<QuizRulesResponse> => {
  const response = await api.get(`/question-bank/quiz/${quizId}/rules`);
  return response.data;
};

export const getQuizRule = async (id: number): Promise<QuizRuleResponse> => {
  const response = await api.get(`/question-bank/quiz-rules/${id}`);
  return response.data;
};

export const createQuizRule = async (quizId: number, data: CreateQuizRuleRequest): Promise<QuizRuleResponse> => {
  const response = await api.post(`/question-bank/quiz/${quizId}/rules`, data);
  return response.data;
};

export const updateQuizRule = async (id: number, data: UpdateQuizRuleRequest): Promise<QuizRuleResponse> => {
  const response = await api.put(`/question-bank/quiz-rules/${id}`, data);
  return response.data;
};

export const deleteQuizRule = async (id: number): Promise<void> => {
  await api.delete(`/question-bank/quiz-rules/${id}`);
};

export const previewQuizQuestions = async (quizId: number): Promise<PreviewResponse> => {
  const response = await api.get(`/question-bank/quiz/${quizId}/preview`);
  return response.data;
};

export const validateQuizRules = async (quizId: number): Promise<ValidationResponse> => {
  const response = await api.get(`/question-bank/quiz/${quizId}/validate`);
  return response.data;
};

// Quiz Generation API functions
export const generateQuizQuestions = async (attemptId: number): Promise<GenerateQuestionsResponse> => {
  const response = await api.post(`/question-bank/quiz-attempt/${attemptId}/generate`);
  return response.data;
};

export const getQuizAttemptQuestions = async (attemptId: number): Promise<QuizAttemptQuestionsResponse> => {
  const response = await api.get(`/question-bank/quiz-attempt/${attemptId}/questions`);
  return response.data;
};
