import api from './authService';
import { type Slide, type Lesson } from '../types';

export interface SlidesResponse {
  slides: Slide[];
}

export interface SlideResponse {
  slide: Slide;
}

export interface LessonResponse {
  lesson: Lesson;
}

export const slideService = {
  async getSlidesByLesson(lessonId: number): Promise<Slide[]> {
    try {
      const response = await api.get<SlidesResponse>(`/slides/lesson/${lessonId}`);
      return response.data.slides || [];
    } catch (error) {
      console.error('Error fetching slides:', error);
      throw error;
    }
  },

  async getSlidesByLessonId(lessonId: number): Promise<Slide[]> {
    return this.getSlidesByLesson(lessonId);
  },

  async getSlide(id: number): Promise<Slide> {
    try {
      const response = await api.get<SlideResponse>(`/slides/${id}`);
      return response.data.slide;
    } catch (error) {
      console.error('Error fetching slide:', error);
      throw error;
    }
  },

  async getLesson(id: number): Promise<Lesson> {
    try {
      const response = await api.get<LessonResponse>(`/lessons/${id}`);
      return response.data.lesson;
    } catch (error) {
      console.error('Error fetching lesson:', error);
      throw error;
    }
  },

  async markSlideComplete(slideId: number): Promise<void> {
    try {
      await api.post(`/progress/slide/${slideId}/complete`);
    } catch (error) {
      console.error('Error marking slide complete:', error);
      throw error;
    }
  },

  async createSlide(slideData: {
    lesson_id: number;
    title?: string;
    content: string;
    image_url?: string;
    order_index: number;
  }): Promise<Slide> {
    try {
      // Đảm bảo order_index luôn là số
      const dataToSend = {
        ...slideData,
        order_index: Number(slideData.order_index)
      };

      console.log('Sending slide data to API:', dataToSend);
      const response = await api.post<SlideResponse>('/slides', dataToSend);
      console.log('API response:', response.data);
      return response.data.slide;
    } catch (error) {
      console.error('Error creating slide:', error);
      throw error;
    }
  },

  async updateSlide(id: number, slideData: {
    title?: string;
    content?: string;
    image_url?: string;
    order_index?: number;
  }): Promise<Slide> {
    try {
      const response = await api.put<SlideResponse>(`/slides/${id}`, slideData);
      return response.data.slide;
    } catch (error) {
      console.error('Error updating slide:', error);
      throw error;
    }
  },

  async deleteSlide(id: number): Promise<void> {
    try {
      await api.delete(`/slides/${id}`);
    } catch (error) {
      console.error('Error deleting slide:', error);
      throw error;
    }
  },
};
