import { Routes, Route, Navigate } from 'react-router-dom';
import ProtectedRoute from './components/ProtectedRoute';
import DashboardLayout from './components/DashboardLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Courses from './pages/Courses';
import Students from './pages/Students';
import Users from './pages/Users';
import LessonViewer from './pages/LessonViewer';
import CourseDetail from './pages/CourseDetail';
import QuizAttempt from './pages/QuizAttempt';
import Quizzes from './pages/Quizzes';
import QuizEdit from './pages/QuizEdit';
import QuestionBank from './pages/QuestionBank';

import CourseEdit from './pages/CourseEdit';
import SlideCreate from './pages/SlideCreate';
import SlideEdit from './pages/SlideEdit';
import SlideManagement from './pages/SlideManagement';
import SlideEditorDemo from './pages/SlideEditorDemo';
import NavigationTest from './pages/NavigationTest';
import OrderIndexTest from './pages/OrderIndexTest';

// Admin-only route wrapper
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  if (user.role !== 'admin') {
    return (
      <div className="text-center py-5">
        <h3>Access Denied</h3>
        <p className="text-muted">You don't have permission to access this page.</p>
      </div>
    );
  }

  return <>{children}</>;
};

const Analytics = () => (
  <div className="text-center py-5">
    <h2>Analytics</h2>
    <p className="text-muted">Analytics dashboard coming soon...</p>
  </div>
);

const Settings = () => (
  <div className="text-center py-5">
    <h2>Settings</h2>
    <p className="text-muted">Settings panel coming soon...</p>
  </div>
);

function App() {
  return (
    <div className="app">
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<Login />} />

        {/* Protected Dashboard Routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <DashboardLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="courses" element={<Courses />} />
          <Route path="courses/:courseId" element={<CourseDetail />} />
          <Route path="courses/:courseId/edit" element={
            <ProtectedRoute>
              <CourseEdit />
            </ProtectedRoute>
          } />
          <Route path="lessons/:lessonId" element={<LessonViewer />} />
          <Route path="lessons/:lessonId/slides/create" element={
            <ProtectedRoute>
              <SlideCreate />
            </ProtectedRoute>
          } />
          <Route path="slides/:slideId/edit" element={
            <ProtectedRoute>
              <SlideEdit />
            </ProtectedRoute>
          } />
          <Route path="lessons/:lessonId/slides" element={
            <ProtectedRoute>
              <SlideManagement />
            </ProtectedRoute>
          } />
          <Route path="slide-editor-demo" element={
            <ProtectedRoute>
              <SlideEditorDemo />
            </ProtectedRoute>
          } />
          <Route path="navigation-test" element={
            <ProtectedRoute>
              <NavigationTest />
            </ProtectedRoute>
          } />
          <Route path="order-index-test" element={
            <ProtectedRoute>
              <OrderIndexTest />
            </ProtectedRoute>
          } />
          <Route path="students" element={<Students />} />
          <Route path="users" element={<AdminRoute><Users /></AdminRoute>} />
          <Route path="quizzes" element={<Quizzes />} />
          <Route path="quizzes/:quizId/edit" element={<QuizEdit />} />
          <Route path="question-bank" element={<QuestionBank />} />
          <Route path="analytics" element={<Analytics />} />
          <Route path="settings" element={<Settings />} />
          <Route path="quizzes/:quizId/attempt" element={
            <ProtectedRoute>
              <QuizAttempt />
            </ProtectedRoute>
          } />
        </Route>

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </div>
  );
}

export default App;
