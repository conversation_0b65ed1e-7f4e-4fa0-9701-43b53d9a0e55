# Tích hợp Slide Editor vào Course Edit

## Tổng quan

Đã tích hợp thành công Slide Editor với Markdown live preview vào màn hình Course Edit, cho phép giáo viên và admin tạo và quản lý slides một cách trực quan và hiệu quả.

## Tính năng mới trong Course Edit

### 1. G<PERSON>o diện Slides được cải thiện

#### Header với các nút hành động:
- **View**: Xem slides trong chế độ presentation
- **Manage**: Vào trang quản lý slides chi tiết
- **Create with Editor**: Tạo slide mới bằng Markdown Editor

#### Hiển thị slides với preview:
- **Markdown Preview**: Hiển thị nội dung slide đã render
- **Metadata**: Title, order index, ngày tạo
- **Actions**: Edit với Markdown Editor, Delete

### 2. <PERSON><PERSON><PERSON> slides với nhiều tùy chọn

#### Option 1: Markdown Editor (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị)
```
<PERSON>út "Create with Markdown Editor"
→ Chuyển đến trang SlideCreate
→ Full-featured editor với live preview
→ Split-screen layout
→ Real-time markdown rendering
```

#### Option 2: Quick Add
```
Form ngay trong CourseEdit
→ Textarea đơn giản
→ Hỗ trợ Markdown cơ bản
→ Tạo nhanh cho nội dung đơn giản
```

### 3. Chỉnh sửa slides

#### Edit với Markdown Editor:
- Click nút Edit (icon Edit3) trên mỗi slide
- Chuyển đến trang SlideEdit với editor đầy đủ
- Load nội dung hiện tại
- Save và quay lại CourseEdit

## Cách sử dụng

### Bước 1: Truy cập Course Edit
1. Vào trang Courses
2. Click "Edit" trên course cần chỉnh sửa
3. Chuyển sang tab "Lessons & Slides"

### Bước 2: Quản lý Lessons
1. Tạo lesson mới bằng nút "Add Lesson"
2. Chọn lesson để quản lý slides
3. Edit/Delete lessons nếu cần

### Bước 3: Tạo Slides

#### Cách 1: Sử dụng Markdown Editor (Khuyến nghị)
1. Click "Create with Editor" trong header
2. Hoặc click nút lớn "Create with Markdown Editor"
3. Sử dụng editor với live preview
4. Save và quay lại

#### Cách 2: Quick Add
1. Scroll xuống phần "Quick Add Slide"
2. Nhập title (optional)
3. Nhập content (Markdown)
4. Nhập image URL (optional)
5. Click "Quick Add"

### Bước 4: Quản lý Slides
1. Xem preview markdown của mỗi slide
2. Click Edit (icon Edit3) để chỉnh sửa với editor
3. Click Delete (icon Trash2) để xóa
4. Sử dụng các nút header để view/manage

## Tính năng chi tiết

### Markdown Preview trong Course Edit
```css
- Border và background để phân biệt
- Font size nhỏ hơn (14px)
- Truncate content dài (300 chars)
- "View more..." link để mở editor
- Responsive design
```

### Navigation Flow
```
CourseEdit → SlideCreate → CourseEdit
CourseEdit → SlideEdit → CourseEdit
CourseEdit → SlideManagement → CourseEdit
CourseEdit → LessonViewer (View slides)
```

### State Management
- Auto-refresh slides sau khi create/edit/delete
- Maintain selected lesson
- Error handling và loading states
- Form validation

## API Integration

### Endpoints được sử dụng:
```typescript
// Tạo slide
POST /slides
{
  lesson_id: number,
  title: string,
  content: string,
  image_url?: string,
  order_index: number
}

// Cập nhật slide
PUT /slides/:id

// Xóa slide
DELETE /slides/:id

// Lấy slides của lesson
GET /slides/lesson/:lessonId
```

### Auto-generated order_index:
- Tự động tính toán order_index tiếp theo
- Bắt đầu từ 1 (không phải 0)
- Sắp xếp slides theo order_index

## Styling và UX

### Custom CSS cho slide preview:
```css
.slide-preview .markdown-body {
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  padding: 1rem;
  background-color: #f8f9fa;
  font-size: 14px;
}
```

### Bootstrap Components:
- Cards cho layout
- Badges cho order index
- Buttons với icons
- Alerts cho messages
- Responsive grid system

### Icons (Lucide React):
- `FileText`: Slides header
- `Edit3`: Markdown editor actions
- `Eye`: View slides
- `Edit`: Manage/edit actions
- `Plus`: Create actions
- `Trash2`: Delete actions

## Error Handling

### Validation:
- Content required cho slides
- Title required cho lessons
- Lesson selection required

### Error Messages:
- API errors hiển thị trong alerts
- Form validation feedback
- Loading states với spinners

### User Feedback:
- Success messages sau actions
- Confirmation dialogs cho delete
- Disabled states khi saving

## Performance

### Optimizations:
- Lazy loading cho markdown preview
- Debounced auto-save (future)
- Efficient re-renders
- Minimal API calls

### Bundle Size:
- Reuse existing dependencies
- No additional heavy libraries
- Shared components

## Browser Support

### Tested on:
- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support
- Mobile: Responsive design

## Future Enhancements

### Planned Features:
- [ ] Drag & drop reordering
- [ ] Bulk operations
- [ ] Slide templates
- [ ] Auto-save drafts
- [ ] Collaboration features
- [ ] Export options

### Performance Improvements:
- [ ] Virtual scrolling for large lists
- [ ] Lazy loading for previews
- [ ] Caching strategies

## Troubleshooting

### Common Issues:

1. **Slides không hiển thị**
   - Kiểm tra lesson đã được chọn
   - Refresh trang nếu cần

2. **Preview không render**
   - Kiểm tra markdown syntax
   - Xem console để debug

3. **Navigation không hoạt động**
   - Kiểm tra routes đã được setup
   - Verify permissions

### Debug Tips:
- Mở Developer Tools
- Check Network tab cho API calls
- Xem Console cho JavaScript errors

## Kết luận

Tích hợp Slide Editor vào Course Edit đã tạo ra một workflow hoàn chỉnh và trực quan cho việc tạo và quản lý nội dung giáo dục. Giáo viên có thể:

1. **Tạo slides dễ dàng** với Markdown Editor
2. **Xem preview ngay lập tức** trong Course Edit
3. **Quản lý toàn bộ course** từ một màn hình
4. **Chuyển đổi linh hoạt** giữa các chế độ edit/view

Hệ thống này cung cấp trải nghiệm người dùng mượt mà và hiệu quả cho việc tạo nội dung giáo dục chất lượng cao.
